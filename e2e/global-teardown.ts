import { chromium, FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown...')
  
  // Launch browser for teardown
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Perform any global cleanup tasks here
    // For example, clean up test data, reset database state, etc.
    
    console.log('🗑️ Cleaning up test data...')
    
    // Example: Clean up test data
    // await page.request.post('/api/test/cleanup', {
    //   data: { action: 'cleanup-test-data' }
    // })
    
    // Clear any cached data
    await page.context().clearCookies()
    await page.context().clearPermissions()
    
    console.log('✅ Global teardown completed successfully!')
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error)
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await browser.close()
  }
}

export default globalTeardown
