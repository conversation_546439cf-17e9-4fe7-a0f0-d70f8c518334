import { test, expect } from '@playwright/test'

test.describe('Home Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display the main heading', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /find your dream home/i })).toBeVisible()
  })

  test('should have navigation menu', async ({ page }) => {
    await expect(page.getByRole('link', { name: /properties/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /neighborhoods/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /about/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /contact/i })).toBeVisible()
  })

  test('should display search form', async ({ page }) => {
    await expect(page.getByPlaceholder(/search by city/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /search/i })).toBeVisible()
  })

  test('should display featured properties section', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /featured properties/i })).toBeVisible()
    
    // Check if at least one property card is visible
    const propertyCards = page.locator('[data-testid="property-card"]')
    await expect(propertyCards.first()).toBeVisible()
  })

  test('should display neighborhood guide section', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /explore collin county neighborhoods/i })).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if mobile menu button is visible
    const mobileMenuButton = page.getByRole('button', { name: /menu/i })
    await expect(mobileMenuButton).toBeVisible()
    
    // Check if main content is still visible
    await expect(page.getByRole('heading', { name: /find your dream home/i })).toBeVisible()
  })

  test('should navigate to properties page', async ({ page }) => {
    await page.getByRole('link', { name: /properties/i }).first().click()
    await expect(page).toHaveURL(/\/properties/)
    await expect(page.getByRole('heading', { name: /properties for sale/i })).toBeVisible()
  })

  test('should navigate to neighborhoods page', async ({ page }) => {
    await page.getByRole('link', { name: /neighborhoods/i }).first().click()
    await expect(page).toHaveURL(/\/neighborhoods/)
    await expect(page.getByRole('heading', { name: /explore collin county neighborhoods/i })).toBeVisible()
  })

  test('should navigate to contact page', async ({ page }) => {
    await page.getByRole('link', { name: /contact/i }).first().click()
    await expect(page).toHaveURL(/\/contact/)
    await expect(page.getByRole('heading', { name: /contact our real estate experts/i })).toBeVisible()
  })

  test('should perform search functionality', async ({ page }) => {
    const searchInput = page.getByPlaceholder(/search by city/i)
    await searchInput.fill('Plano')
    
    const searchButton = page.getByRole('button', { name: /search/i })
    await searchButton.click()
    
    // Should navigate to properties page with search query
    await expect(page).toHaveURL(/\/properties/)
  })

  test('should display footer', async ({ page }) => {
    await expect(page.getByRole('contentinfo')).toBeVisible()
    await expect(page.getByText(/collin county/i)).toBeVisible()
  })

  test('should have proper meta tags', async ({ page }) => {
    await expect(page).toHaveTitle(/collin county homes/i)
    
    const description = await page.locator('meta[name="description"]').getAttribute('content')
    expect(description).toContain('dream home')
  })

  test('should load without accessibility violations', async ({ page }) => {
    // Basic accessibility check - ensure no missing alt texts on images
    const images = page.locator('img')
    const imageCount = await images.count()
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i)
      const alt = await img.getAttribute('alt')
      expect(alt).toBeTruthy()
    }
  })

  test('should handle keyboard navigation', async ({ page }) => {
    // Test tab navigation through main elements
    await page.keyboard.press('Tab')
    await expect(page.getByRole('link', { name: /properties/i }).first()).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByRole('link', { name: /neighborhoods/i }).first()).toBeFocused()
  })
})
