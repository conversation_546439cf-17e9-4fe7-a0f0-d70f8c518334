{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Image from 'next/image'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Search, MapPin, TrendingUp, Users } from 'lucide-react'\n\nexport function HeroSection() {\n  const [searchQuery, setSearchQuery] = useState('')\n\n  const stats = [\n    { icon: MapPin, label: 'Cities Served', value: '25+' },\n    { icon: TrendingUp, label: 'Properties Sold', value: '2,500+' },\n    { icon: Users, label: 'Happy Families', value: '1,800+' },\n  ]\n\n  return (\n    <section className=\"relative min-h-[80vh] flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0 z-0\">\n        <Image\n          src=\"/hero-bg.jpg\"\n          alt=\"Beautiful Collin County neighborhood\"\n          fill\n          className=\"object-cover\"\n          priority\n          placeholder=\"blur\"\n          blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n        />\n        <div className=\"absolute inset-0 bg-black/40\" />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"space-y-8\">\n          {/* Main Heading */}\n          <div className=\"space-y-4\">\n            <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight\">\n              Find Your Dream Home in{' '}\n              <span className=\"text-blue-400\">Collin County</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto\">\n              Discover luxury homes, family-friendly neighborhoods, and exceptional schools \n              in Texas&apos;s most desirable communities.\n            </p>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"max-w-2xl mx-auto\">\n            <div className=\"bg-white rounded-lg p-4 shadow-2xl\">\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"flex-1 relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                  <Input\n                    type=\"text\"\n                    placeholder=\"Search by city, neighborhood, or address...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"pl-10 h-12 text-lg border-0 focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                <Button size=\"lg\" className=\"h-12 px-8 bg-blue-600 hover:bg-blue-700\">\n                  Search Homes\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {['Plano', 'Frisco', 'McKinney', 'Allen', 'Prosper'].map((city) => (\n              <Button\n                key={city}\n                variant=\"outline\"\n                className=\"bg-white/10 border-white/20 text-white hover:bg-white/20\"\n              >\n                Homes in {city}\n              </Button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Section */}\n      <div className=\"absolute bottom-0 left-0 right-0 z-10\">\n        <div className=\"bg-white/95 backdrop-blur-sm border-t\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {stats.map((stat, index) => (\n                <div key={index} className=\"flex items-center justify-center space-x-3\">\n                  <div className=\"p-2 bg-blue-100 rounded-lg\">\n                    <stat.icon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div>\n                    <div className=\"text-2xl font-bold text-gray-900\">{stat.value}</div>\n                    <div className=\"text-sm text-gray-600\">{stat.label}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,QAAQ;QACZ;YAAE,MAAM,6MAAA,CAAA,SAAM;YAAE,OAAO;YAAiB,OAAO;QAAM;QACrD;YAAE,MAAM,qNAAA,CAAA,aAAU;YAAE,OAAO;YAAmB,OAAO;QAAS;QAC9D;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;YAAkB,OAAO;QAAS;KACzD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,WAAU;wBACV,QAAQ;wBACR,aAAY;wBACZ,aAAY;;;;;;kCAEd,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAsE;wCAC1D;sDACxB,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,6LAAC;oCAAE,WAAU;8CAAsD;;;;;;;;;;;;sCAOrE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;sDAGd,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;;;;sCAQ5E,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAS;gCAAU;gCAAY;gCAAS;6BAAU,CAAC,GAAG,CAAC,CAAC,qBACxD,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,WAAU;;wCACX;wCACW;;mCAJL;;;;;;;;;;;;;;;;;;;;;0BAYf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAoC,KAAK,KAAK;;;;;;8DAC7D,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,KAAK;;;;;;;;;;;;;mCAN5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB1B;GAlGgB;KAAA", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-slate-200 bg-white text-slate-950 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-500\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nfunction Badge({ className, variant = \"default\", ...props }: BadgeProps) {\n  return (\n    <div\n      className={cn(\n        \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2\",\n        {\n          \"border-transparent bg-slate-900 text-slate-50 hover:bg-slate-900/80\": variant === \"default\",\n          \"border-transparent bg-slate-100 text-slate-900 hover:bg-slate-100/80\": variant === \"secondary\",\n          \"border-transparent bg-red-500 text-slate-50 hover:bg-red-500/80\": variant === \"destructive\",\n          \"text-slate-950\": variant === \"outline\",\n        },\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Badge }\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACrE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+KACA;YACE,uEAAuE,YAAY;YACnF,wEAAwE,YAAY;YACpF,mEAAmE,YAAY;YAC/E,kBAAkB,YAAY;QAChC,GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/PropertyCard.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport Link from 'next/link'\nimport { Property } from '@/types'\nimport { formatPrice, formatNumber } from '@/lib/utils'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Bed, Bath, Square, MapPin, Heart } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\ninterface PropertyCardProps {\n  property: Property\n  onFavorite?: (propertyId: string) => void\n  isFavorited?: boolean\n}\n\nexport function PropertyCard({ property, onFavorite, isFavorited = false }: PropertyCardProps) {\n  const primaryImage = property.images[0] || '/placeholder-property.jpg'\n\n  return (\n    <Card className=\"overflow-hidden hover:shadow-lg transition-shadow duration-300\">\n      <div className=\"relative\">\n        <Link href={`/properties/${property.id}`}>\n          <div className=\"relative h-48 w-full\">\n            <Image\n              src={primaryImage}\n              alt={property.title}\n              fill\n              className=\"object-cover\"\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n            />\n          </div>\n        </Link>\n\n        {/* Status Badge */}\n        <div className=\"absolute top-3 left-3\">\n          <Badge\n            variant={property.status === 'for-sale' ? 'default' : 'secondary'}\n            className=\"capitalize\"\n          >\n            {property.status.replace('-', ' ')}\n          </Badge>\n        </div>\n\n        {/* Favorite Button */}\n        {onFavorite && (\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"absolute top-3 right-3 bg-white/80 hover:bg-white\"\n            onClick={() => onFavorite(property.id)}\n          >\n            <Heart\n              className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : 'text-gray-600'}`}\n            />\n          </Button>\n        )}\n\n        {/* Days on Market */}\n        {property.daysOnMarket && (\n          <div className=\"absolute bottom-3 left-3\">\n            <Badge variant=\"outline\" className=\"bg-white/90\">\n              {property.daysOnMarket} days on market\n            </Badge>\n          </div>\n        )}\n      </div>\n\n      <CardContent className=\"p-4\">\n        <div className=\"space-y-3\">\n          {/* Price */}\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-2xl font-bold text-green-600\">\n              {formatPrice(property.price)}\n            </h3>\n            {property.mlsNumber && (\n              <span className=\"text-sm text-gray-500\">\n                MLS# {property.mlsNumber}\n              </span>\n            )}\n          </div>\n\n          {/* Property Details */}\n          <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n            <div className=\"flex items-center space-x-1\">\n              <Bed className=\"h-4 w-4\" />\n              <span>{property.bedrooms} bed</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Bath className=\"h-4 w-4\" />\n              <span>{property.bathrooms} bath</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Square className=\"h-4 w-4\" />\n              <span>{formatNumber(property.sqft || property.squareFootage || 0)} sqft</span>\n            </div>\n          </div>\n\n          {/* Address */}\n          <div className=\"flex items-start space-x-1 text-sm text-gray-600\">\n            <MapPin className=\"h-4 w-4 mt-0.5 flex-shrink-0\" />\n            <span className=\"line-clamp-2\">\n              {property.address}\n            </span>\n          </div>\n\n          {/* Property Type */}\n          <div className=\"flex items-center justify-between\">\n            <Badge variant=\"outline\" className=\"capitalize\">\n              {(property.type || property.propertyType || 'house').replace('-', ' ')}\n            </Badge>\n            {property.yearBuilt && (\n              <span className=\"text-sm text-gray-500\">\n                Built {property.yearBuilt}\n              </span>\n            )}\n          </div>\n\n          {/* Description Preview */}\n          <p className=\"text-sm text-gray-600 line-clamp-2\">\n            {property.description}\n          </p>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;;;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,KAAK,EAAqB;IAC3F,MAAM,eAAe,SAAS,MAAM,CAAC,EAAE,IAAI;IAE3C,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;kCACtC,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK,SAAS,KAAK;gCACnB,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;;;;;;;;;;;kCAMZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAS,SAAS,MAAM,KAAK,aAAa,YAAY;4BACtD,WAAU;sCAET,SAAS,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;oBAKjC,4BACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,WAAW,SAAS,EAAE;kCAErC,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BACJ,WAAW,CAAC,QAAQ,EAAE,cAAc,8BAA8B,iBAAiB;;;;;;;;;;;oBAMxF,SAAS,YAAY,kBACpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;;gCAChC,SAAS,YAAY;gCAAC;;;;;;;;;;;;;;;;;;0BAM/B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,KAAK;;;;;;gCAE5B,SAAS,SAAS,kBACjB,6LAAC;oCAAK,WAAU;;wCAAwB;wCAChC,SAAS,SAAS;;;;;;;;;;;;;sCAM9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;;gDAAM,SAAS,QAAQ;gDAAC;;;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;;gDAAM,SAAS,SAAS;gDAAC;;;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;;gDAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,IAAI,IAAI,SAAS,aAAa,IAAI;gDAAG;;;;;;;;;;;;;;;;;;;sCAKtE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAK,WAAU;8CACb,SAAS,OAAO;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAChC,CAAC,SAAS,IAAI,IAAI,SAAS,YAAY,IAAI,OAAO,EAAE,OAAO,CAAC,KAAK;;;;;;gCAEnE,SAAS,SAAS,kBACjB,6LAAC;oCAAK,WAAU;;wCAAwB;wCAC/B,SAAS,SAAS;;;;;;;;;;;;;sCAM/B,6LAAC;4BAAE,WAAU;sCACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAMjC;KA9GgB", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { PropertyCard } from '@/components/PropertyCard'\nimport { Button } from '@/components/ui/button'\nimport { ArrowRight } from 'lucide-react'\n\n// Mock data for featured properties\nconst featuredProperties = [\n  {\n    id: '1',\n    title: 'Luxury Estate in Plano',\n    price: 850000,\n    address: '123 Oak Tree Lane, Plano, TX 75024',\n    bedrooms: 5,\n    bathrooms: 4.5,\n    sqft: 4200,\n    images: ['/property-1.jpg'],\n    type: 'house' as const,\n    status: 'for-sale' as const,\n    features: ['Pool', 'Garage', 'Fireplace', 'Updated Kitchen'],\n    description: 'Stunning luxury home in prestigious Plano neighborhood with top-rated schools.',\n    yearBuilt: 2018,\n    lotSize: 0.75,\n    garage: 3,\n    neighborhood: 'West Plano',\n    schools: {\n      elementary: 'Plano Elementary (9/10)',\n      middle: 'Plano Middle School (8/10)',\n      high: 'Plano Senior High (9/10)'\n    }\n  },\n  {\n    id: '2',\n    title: 'Modern Townhome in Frisco',\n    price: 425000,\n    address: '456 Maple Street, Frisco, TX 75034',\n    bedrooms: 3,\n    bathrooms: 2.5,\n    sqft: 2100,\n    images: ['/property-2.jpg'],\n    type: 'townhouse' as const,\n    status: 'for-sale' as const,\n    features: ['Patio', 'Garage', 'Open Floor Plan'],\n    description: 'Beautiful modern townhome in the heart of Frisco with easy access to shopping and dining.',\n    yearBuilt: 2020,\n    lotSize: 0.15,\n    garage: 2,\n    neighborhood: 'Frisco Square',\n    schools: {\n      elementary: 'Frisco Elementary (9/10)',\n      middle: 'Frisco Middle School (9/10)',\n      high: 'Frisco High School (8/10)'\n    }\n  },\n  {\n    id: '3',\n    title: 'Family Home in McKinney',\n    price: 375000,\n    address: '789 Pine Avenue, McKinney, TX 75070',\n    bedrooms: 4,\n    bathrooms: 3,\n    sqft: 2800,\n    images: ['/property-3.jpg'],\n    type: 'house' as const,\n    status: 'for-sale' as const,\n    features: ['Large Yard', 'Garage', 'Updated Bathrooms'],\n    description: 'Perfect family home in established McKinney neighborhood with mature trees.',\n    yearBuilt: 2015,\n    lotSize: 0.5,\n    garage: 2,\n    neighborhood: 'Historic McKinney',\n    schools: {\n      elementary: 'McKinney Elementary (8/10)',\n      middle: 'McKinney Middle School (8/10)',\n      high: 'McKinney High School (7/10)'\n    }\n  },\n  {\n    id: '4',\n    title: 'New Construction in Allen',\n    price: 525000,\n    address: '321 Cedar Drive, Allen, TX 75013',\n    bedrooms: 4,\n    bathrooms: 3.5,\n    sqft: 3200,\n    images: ['/property-4.jpg'],\n    type: 'house' as const,\n    status: 'for-sale' as const,\n    features: ['New Construction', 'Smart Home', 'Energy Efficient'],\n    description: 'Brand new construction with all the latest features and smart home technology.',\n    yearBuilt: 2024,\n    lotSize: 0.4,\n    garage: 2,\n    neighborhood: 'Allen Station',\n    schools: {\n      elementary: 'Allen Elementary (9/10)',\n      middle: 'Allen Middle School (9/10)',\n      high: 'Allen High School (9/10)'\n    }\n  },\n  {\n    id: '5',\n    title: 'Luxury Condo in Prosper',\n    price: 295000,\n    address: '654 Willow Creek, Prosper, TX 75078',\n    bedrooms: 2,\n    bathrooms: 2,\n    sqft: 1400,\n    images: ['/property-5.jpg'],\n    type: 'condo' as const,\n    status: 'for-sale' as const,\n    features: ['Balcony', 'Pool Access', 'Fitness Center'],\n    description: 'Luxury condo with resort-style amenities in growing Prosper community.',\n    yearBuilt: 2021,\n    lotSize: 0,\n    garage: 1,\n    neighborhood: 'Prosper Commons',\n    schools: {\n      elementary: 'Prosper Elementary (9/10)',\n      middle: 'Prosper Middle School (9/10)',\n      high: 'Prosper High School (9/10)'\n    }\n  },\n  {\n    id: '6',\n    title: 'Executive Home in Richardson',\n    price: 675000,\n    address: '987 Elm Street, Richardson, TX 75081',\n    bedrooms: 5,\n    bathrooms: 4,\n    sqft: 3800,\n    images: ['/property-6.jpg'],\n    type: 'house' as const,\n    status: 'for-sale' as const,\n    features: ['Pool', 'Home Office', 'Wine Cellar'],\n    description: 'Executive home with premium finishes and resort-style backyard.',\n    yearBuilt: 2017,\n    lotSize: 0.6,\n    garage: 3,\n    neighborhood: 'Richardson Heights',\n    schools: {\n      elementary: 'Richardson Elementary (8/10)',\n      middle: 'Richardson Middle School (8/10)',\n      high: 'Richardson High School (8/10)'\n    }\n  }\n]\n\nexport function FeaturedProperties() {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Featured Properties\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Discover our handpicked selection of premium homes in Collin County&apos;s most desirable neighborhoods\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {featuredProperties.map((property) => (\n            <PropertyCard key={property.id} property={property} />\n          ))}\n        </div>\n\n        <div className=\"text-center\">\n          <Button size=\"lg\" variant=\"outline\" className=\"px-8\">\n            View All Properties\n            <ArrowRight className=\"ml-2 h-5 w-5\" />\n          </Button>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,oCAAoC;AACpC,MAAM,qBAAqB;IACzB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QACX,MAAM;QACN,QAAQ;YAAC;SAAkB;QAC3B,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAQ;YAAU;YAAa;SAAkB;QAC5D,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;QACd,SAAS;YACP,YAAY;YACZ,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QACX,MAAM;QACN,QAAQ;YAAC;SAAkB;QAC3B,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAS;YAAU;SAAkB;QAChD,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;QACd,SAAS;YACP,YAAY;YACZ,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QACX,MAAM;QACN,QAAQ;YAAC;SAAkB;QAC3B,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAc;YAAU;SAAoB;QACvD,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;QACd,SAAS;YACP,YAAY;YACZ,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QACX,MAAM;QACN,QAAQ;YAAC;SAAkB;QAC3B,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAoB;YAAc;SAAmB;QAChE,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;QACd,SAAS;YACP,YAAY;YACZ,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QACX,MAAM;QACN,QAAQ;YAAC;SAAkB;QAC3B,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAW;YAAe;SAAiB;QACtD,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;QACd,SAAS;YACP,YAAY;YACZ,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QACX,MAAM;QACN,QAAQ;YAAC;SAAkB;QAC3B,MAAM;QACN,QAAQ;QACR,UAAU;YAAC;YAAQ;YAAe;SAAc;QAChD,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;QACd,SAAS;YACP,YAAY;YACZ,QAAQ;YACR,MAAM;QACR;IACF;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC,qIAAA,CAAA,eAAY;4BAAmB,UAAU;2BAAvB,SAAS,EAAE;;;;;;;;;;8BAIlC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,WAAU;;4BAAO;0CAEnD,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA5BgB", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card } from '@/components/ui/card'\nimport { Search, MapPin, Home, DollarSign, Bed, Bath, Square } from 'lucide-react'\n\nexport function SearchSection() {\n  const [searchType, setSearchType] = useState<'buy' | 'rent'>('buy')\n  const [filters, setFilters] = useState({\n    location: '',\n    priceMin: '',\n    priceMax: '',\n    bedrooms: '',\n    bathrooms: '',\n    propertyType: 'any'\n  })\n\n  const propertyTypes = [\n    { value: 'any', label: 'Any Type' },\n    { value: 'house', label: 'House' },\n    { value: 'condo', label: 'Condo' },\n    { value: 'townhouse', label: 'Townhouse' },\n    { value: 'land', label: 'Land' }\n  ]\n\n  const bedroomOptions = ['Any', '1+', '2+', '3+', '4+', '5+']\n  const bathroomOptions = ['Any', '1+', '1.5+', '2+', '2.5+', '3+', '4+']\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Advanced Property Search\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Find your perfect home with our comprehensive search filters\n          </p>\n        </div>\n\n        <Card className=\"p-8 shadow-xl\">\n          {/* Search Type Toggle */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-gray-100 p-1 rounded-lg\">\n              <button\n                onClick={() => setSearchType('buy')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  searchType === 'buy'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Buy\n              </button>\n              <button\n                onClick={() => setSearchType('rent')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  searchType === 'rent'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Rent\n              </button>\n            </div>\n          </div>\n\n          {/* Search Form */}\n          <div className=\"space-y-6\">\n            {/* Location Search */}\n            <div className=\"relative\">\n              <MapPin className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n              <Input\n                type=\"text\"\n                placeholder=\"Enter city, neighborhood, or address\"\n                value={filters.location}\n                onChange={(e) => setFilters({ ...filters, location: e.target.value })}\n                className=\"pl-10 h-12 text-lg\"\n              />\n            </div>\n\n            {/* Filters Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Price Range */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium text-gray-700\">\n                  Price Range\n                </label>\n                <div className=\"flex space-x-2\">\n                  <div className=\"relative flex-1\">\n                    <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                    <Input\n                      type=\"text\"\n                      placeholder=\"Min\"\n                      value={filters.priceMin}\n                      onChange={(e) => setFilters({ ...filters, priceMin: e.target.value })}\n                      className=\"pl-8\"\n                    />\n                  </div>\n                  <div className=\"relative flex-1\">\n                    <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                    <Input\n                      type=\"text\"\n                      placeholder=\"Max\"\n                      value={filters.priceMax}\n                      onChange={(e) => setFilters({ ...filters, priceMax: e.target.value })}\n                      className=\"pl-8\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Property Type */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium text-gray-700\">\n                  Property Type\n                </label>\n                <select\n                  value={filters.propertyType}\n                  onChange={(e) => setFilters({ ...filters, propertyType: e.target.value })}\n                  className=\"w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  {propertyTypes.map((type) => (\n                    <option key={type.value} value={type.value}>\n                      {type.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Bedrooms */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium text-gray-700\">\n                  Bedrooms\n                </label>\n                <select\n                  value={filters.bedrooms}\n                  onChange={(e) => setFilters({ ...filters, bedrooms: e.target.value })}\n                  className=\"w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  {bedroomOptions.map((option) => (\n                    <option key={option} value={option}>\n                      {option}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Bathrooms */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium text-gray-700\">\n                  Bathrooms\n                </label>\n                <select\n                  value={filters.bathrooms}\n                  onChange={(e) => setFilters({ ...filters, bathrooms: e.target.value })}\n                  className=\"w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  {bathroomOptions.map((option) => (\n                    <option key={option} value={option}>\n                      {option}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Search Button */}\n            <div className=\"flex justify-center pt-4\">\n              <Button size=\"lg\" className=\"px-12 h-12 bg-blue-600 hover:bg-blue-700\">\n                <Search className=\"h-5 w-5 mr-2\" />\n                Search Properties\n              </Button>\n            </div>\n          </div>\n        </Card>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAO,OAAO;QAAW;QAClC;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAQ,OAAO;QAAO;KAChC;IAED,MAAM,iBAAiB;QAAC;QAAO;QAAM;QAAM;QAAM;QAAM;KAAK;IAC5D,MAAM,kBAAkB;QAAC;QAAO;QAAM;QAAQ;QAAM;QAAQ;QAAM;KAAK;IAEvE,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCAEd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,mDAAmD,EAC7D,eAAe,QACX,2BACA,qCACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,mDAAmD,EAC7D,eAAe,SACX,2BACA,qCACJ;kDACH;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACnE,WAAU;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAoC;;;;;;8DAGrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,aAAY;oEACZ,OAAO,QAAQ,QAAQ;oEACvB,UAAU,CAAC,IAAM,WAAW;4EAAE,GAAG,OAAO;4EAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACnE,WAAU;;;;;;;;;;;;sEAGd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,aAAY;oEACZ,OAAO,QAAQ,QAAQ;oEACvB,UAAU,CAAC,IAAM,WAAW;4EAAE,GAAG,OAAO;4EAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACnE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAOlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAoC;;;;;;8DAGrD,6LAAC;oDACC,OAAO,QAAQ,YAAY;oDAC3B,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACvE,WAAU;8DAET,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;4DAAwB,OAAO,KAAK,KAAK;sEACvC,KAAK,KAAK;2DADA,KAAK,KAAK;;;;;;;;;;;;;;;;sDAQ7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAoC;;;;;;8DAGrD,6LAAC;oDACC,OAAO,QAAQ,QAAQ;oDACvB,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACnE,WAAU;8DAET,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;4DAAoB,OAAO;sEACzB;2DADU;;;;;;;;;;;;;;;;sDAQnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAoC;;;;;;8DAGrD,6LAAC;oDACC,OAAO,QAAQ,SAAS;oDACxB,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACpE,WAAU;8DAET,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;4DAAoB,OAAO;sEACzB;2DADU;;;;;;;;;;;;;;;;;;;;;;8CASrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;0DAC1B,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GA7KgB;KAAA", "debugId": null}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { Card } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { MapPin, School, ShoppingBag, Coffee, TreePine, Star } from 'lucide-react'\n\nconst neighborhoods = [\n  {\n    name: 'West Plano',\n    description: 'Prestigious area known for luxury homes and top-rated schools',\n    medianPrice: '$525,000',\n    rating: 9.2,\n    highlights: ['Top Schools', 'Luxury Homes', 'Shopping Centers'],\n    amenities: [\n      { icon: School, label: 'Excellent Schools' },\n      { icon: ShoppingBag, label: 'Legacy West' },\n      { icon: TreePine, label: 'Parks & Trails' }\n    ],\n    image: '/neighborhood-plano.jpg'\n  },\n  {\n    name: 'Frisco Square',\n    description: 'Vibrant downtown area with entertainment, dining, and modern living',\n    medianPrice: '$485,000',\n    rating: 9.0,\n    highlights: ['Entertainment', 'Dining', 'Sports Venues'],\n    amenities: [\n      { icon: Coffee, label: 'Restaurants' },\n      { icon: ShoppingBag, label: 'Shopping' },\n      { icon: Star, label: 'Events' }\n    ],\n    image: '/neighborhood-frisco.jpg'\n  },\n  {\n    name: 'Historic McKinney',\n    description: 'Charming historic downtown with character homes and local businesses',\n    medianPrice: '$425,000',\n    rating: 8.5,\n    highlights: ['Historic Charm', 'Local Businesses', 'Community Events'],\n    amenities: [\n      { icon: Coffee, label: 'Local Cafes' },\n      { icon: TreePine, label: 'Historic Square' },\n      { icon: Star, label: 'Festivals' }\n    ],\n    image: '/neighborhood-mckinney.jpg'\n  },\n  {\n    name: 'Allen Station',\n    description: 'Family-friendly community with excellent schools and recreation',\n    medianPrice: '$465,000',\n    rating: 8.8,\n    highlights: ['Family-Friendly', 'Recreation', 'New Development'],\n    amenities: [\n      { icon: School, label: 'Top Schools' },\n      { icon: TreePine, label: 'Recreation Center' },\n      { icon: ShoppingBag, label: 'Watters Creek' }\n    ],\n    image: '/neighborhood-allen.jpg'\n  },\n  {\n    name: 'Prosper Commons',\n    description: 'Rapidly growing area with new construction and modern amenities',\n    medianPrice: '$595,000',\n    rating: 9.1,\n    highlights: ['New Construction', 'Growth', 'Modern Amenities'],\n    amenities: [\n      { icon: School, label: 'New Schools' },\n      { icon: ShoppingBag, label: 'Shopping Centers' },\n      { icon: TreePine, label: 'Parks' }\n    ],\n    image: '/neighborhood-prosper.jpg'\n  },\n  {\n    name: 'Richardson Heights',\n    description: 'Established community with mature trees and convenient location',\n    medianPrice: '$445,000',\n    rating: 8.3,\n    highlights: ['Established', 'Convenient', 'Mature Trees'],\n    amenities: [\n      { icon: Coffee, label: 'Local Dining' },\n      { icon: TreePine, label: 'Mature Trees' },\n      { icon: MapPin, label: 'Central Location' }\n    ],\n    image: '/neighborhood-richardson.jpg'\n  }\n]\n\nexport function NeighborhoodGuide() {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Explore Collin County Neighborhoods\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Discover the unique character and amenities of each community to find your perfect neighborhood\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {neighborhoods.map((neighborhood, index) => (\n            <Card key={index} className=\"overflow-hidden hover:shadow-xl transition-shadow\">\n              {/* Neighborhood Image */}\n              <div className=\"h-48 bg-gradient-to-br from-blue-400 to-blue-600 relative\">\n                <div className=\"absolute inset-0 bg-black/20\" />\n                <div className=\"absolute top-4 right-4\">\n                  <Badge className=\"bg-white text-gray-900\">\n                    <Star className=\"h-3 w-3 mr-1 fill-current\" />\n                    {neighborhood.rating}\n                  </Badge>\n                </div>\n                <div className=\"absolute bottom-4 left-4\">\n                  <h3 className=\"text-xl font-bold text-white\">{neighborhood.name}</h3>\n                  <p className=\"text-blue-100\">Median: {neighborhood.medianPrice}</p>\n                </div>\n              </div>\n\n              <div className=\"p-6\">\n                <p className=\"text-gray-600 mb-4\">{neighborhood.description}</p>\n\n                {/* Highlights */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {neighborhood.highlights.map((highlight, idx) => (\n                    <Badge key={idx} variant=\"secondary\" className=\"text-xs\">\n                      {highlight}\n                    </Badge>\n                  ))}\n                </div>\n\n                {/* Amenities */}\n                <div className=\"space-y-2 mb-6\">\n                  {neighborhood.amenities.map((amenity, idx) => (\n                    <div key={idx} className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                      <amenity.icon className=\"h-4 w-4 text-blue-600\" />\n                      <span>{amenity.label}</span>\n                    </div>\n                  ))}\n                </div>\n\n                <Button variant=\"outline\" className=\"w-full\">\n                  <MapPin className=\"h-4 w-4 mr-2\" />\n                  Explore {neighborhood.name}\n                </Button>\n              </div>\n            </Card>\n          ))}\n        </div>\n\n        <div className=\"text-center mt-12\">\n          <Button size=\"lg\" className=\"px-8\">\n            View Complete Neighborhood Guide\n          </Button>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,gBAAgB;IACpB;QACE,MAAM;QACN,aAAa;QACb,aAAa;QACb,QAAQ;QACR,YAAY;YAAC;YAAe;YAAgB;SAAmB;QAC/D,WAAW;YACT;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,OAAO;YAAoB;YAC3C;gBAAE,MAAM,uNAAA,CAAA,cAAW;gBAAE,OAAO;YAAc;YAC1C;gBAAE,MAAM,iNAAA,CAAA,WAAQ;gBAAE,OAAO;YAAiB;SAC3C;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,aAAa;QACb,QAAQ;QACR,YAAY;YAAC;YAAiB;YAAU;SAAgB;QACxD,WAAW;YACT;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,OAAO;YAAc;YACrC;gBAAE,MAAM,uNAAA,CAAA,cAAW;gBAAE,OAAO;YAAW;YACvC;gBAAE,MAAM,qMAAA,CAAA,OAAI;gBAAE,OAAO;YAAS;SAC/B;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,aAAa;QACb,QAAQ;QACR,YAAY;YAAC;YAAkB;YAAoB;SAAmB;QACtE,WAAW;YACT;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,OAAO;YAAc;YACrC;gBAAE,MAAM,iNAAA,CAAA,WAAQ;gBAAE,OAAO;YAAkB;YAC3C;gBAAE,MAAM,qMAAA,CAAA,OAAI;gBAAE,OAAO;YAAY;SAClC;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,aAAa;QACb,QAAQ;QACR,YAAY;YAAC;YAAmB;YAAc;SAAkB;QAChE,WAAW;YACT;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,OAAO;YAAc;YACrC;gBAAE,MAAM,iNAAA,CAAA,WAAQ;gBAAE,OAAO;YAAoB;YAC7C;gBAAE,MAAM,uNAAA,CAAA,cAAW;gBAAE,OAAO;YAAgB;SAC7C;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,aAAa;QACb,QAAQ;QACR,YAAY;YAAC;YAAoB;YAAU;SAAmB;QAC9D,WAAW;YACT;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,OAAO;YAAc;YACrC;gBAAE,MAAM,uNAAA,CAAA,cAAW;gBAAE,OAAO;YAAmB;YAC/C;gBAAE,MAAM,iNAAA,CAAA,WAAQ;gBAAE,OAAO;YAAQ;SAClC;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,aAAa;QACb,QAAQ;QACR,YAAY;YAAC;YAAe;YAAc;SAAe;QACzD,WAAW;YACT;gBAAE,MAAM,yMAAA,CAAA,SAAM;gBAAE,OAAO;YAAe;YACtC;gBAAE,MAAM,iNAAA,CAAA,WAAQ;gBAAE,OAAO;YAAe;YACxC;gBAAE,MAAM,6MAAA,CAAA,SAAM;gBAAE,OAAO;YAAmB;SAC3C;QACD,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,6LAAC,mIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAE1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,aAAa,MAAM;;;;;;;;;;;;sDAGxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAgC,aAAa,IAAI;;;;;;8DAC/D,6LAAC;oDAAE,WAAU;;wDAAgB;wDAAS,aAAa,WAAW;;;;;;;;;;;;;;;;;;;8CAIlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAsB,aAAa,WAAW;;;;;;sDAG3D,6LAAC;4CAAI,WAAU;sDACZ,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,oBACvC,6LAAC,oIAAA,CAAA,QAAK;oDAAW,SAAQ;oDAAY,WAAU;8DAC5C;mDADS;;;;;;;;;;sDAOhB,6LAAC;4CAAI,WAAU;sDACZ,aAAa,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,oBACpC,6LAAC;oDAAc,WAAU;;sEACvB,6LAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;sEACxB,6LAAC;sEAAM,QAAQ,KAAK;;;;;;;mDAFZ;;;;;;;;;;sDAOd,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;gDAC1B,aAAa,IAAI;;;;;;;;;;;;;;2BAxCrB;;;;;;;;;;8BA+Cf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,WAAU;kCAAO;;;;;;;;;;;;;;;;;;;;;;AAO7C;KAtEgB", "debugId": null}}, {"offset": {"line": 1909, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { Card } from '@/components/ui/card'\nimport { TrendingUp, TrendingDown, Home, DollarSign, Calendar, Users } from 'lucide-react'\n\nconst marketData = [\n  {\n    title: 'Median Home Price',\n    value: '$485,000',\n    change: '+5.2%',\n    trend: 'up' as const,\n    icon: DollarSign,\n    description: 'vs. last year'\n  },\n  {\n    title: 'Average Days on Market',\n    value: '28 days',\n    change: '-12%',\n    trend: 'down' as const,\n    icon: Calendar,\n    description: 'vs. last year'\n  },\n  {\n    title: 'Homes Sold',\n    value: '2,847',\n    change: '+8.1%',\n    trend: 'up' as const,\n    icon: Home,\n    description: 'this year'\n  },\n  {\n    title: 'Active Listings',\n    value: '1,234',\n    change: '-15%',\n    trend: 'down' as const,\n    icon: Users,\n    description: 'vs. last month'\n  }\n]\n\nconst cityStats = [\n  {\n    city: 'Plano',\n    medianPrice: '$525,000',\n    change: '+4.8%',\n    trend: 'up' as const\n  },\n  {\n    city: 'Frisco',\n    medianPrice: '$485,000',\n    change: '+6.2%',\n    trend: 'up' as const\n  },\n  {\n    city: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    medianPrice: '$425,000',\n    change: '+5.5%',\n    trend: 'up' as const\n  },\n  {\n    city: 'Allen',\n    medianPrice: '$465,000',\n    change: '+4.1%',\n    trend: 'up' as const\n  },\n  {\n    city: 'Prosper',\n    medianPrice: '$595,000',\n    change: '+7.3%',\n    trend: 'up' as const\n  },\n  {\n    city: 'Richardson',\n    medianPrice: '$445,000',\n    change: '+3.9%',\n    trend: 'up' as const\n  }\n]\n\nexport function MarketStats() {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Collin County Market Insights\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Stay informed with the latest real estate market trends and statistics\n          </p>\n        </div>\n\n        {/* Overall Market Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n          {marketData.map((stat, index) => (\n            <Card key={index} className=\"p-6 text-center hover:shadow-lg transition-shadow\">\n              <div className=\"flex justify-center mb-4\">\n                <div className={`p-3 rounded-full ${\n                  stat.trend === 'up' ? 'bg-green-100' : 'bg-red-100'\n                }`}>\n                  <stat.icon className={`h-6 w-6 ${\n                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'\n                  }`} />\n                </div>\n              </div>\n              <h3 className=\"text-sm font-medium text-gray-600 mb-2\">{stat.title}</h3>\n              <div className=\"text-2xl font-bold text-gray-900 mb-2\">{stat.value}</div>\n              <div className=\"flex items-center justify-center space-x-1\">\n                {stat.trend === 'up' ? (\n                  <TrendingUp className=\"h-4 w-4 text-green-600\" />\n                ) : (\n                  <TrendingDown className=\"h-4 w-4 text-red-600\" />\n                )}\n                <span className={`text-sm font-medium ${\n                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'\n                }`}>\n                  {stat.change}\n                </span>\n                <span className=\"text-sm text-gray-500\">{stat.description}</span>\n              </div>\n            </Card>\n          ))}\n        </div>\n\n        {/* City-by-City Breakdown */}\n        <Card className=\"p-8\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">\n            Median Home Prices by City\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {cityStats.map((city, index) => (\n              <div key={index} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                <div>\n                  <h4 className=\"font-semibold text-gray-900\">{city.city}</h4>\n                  <p className=\"text-2xl font-bold text-blue-600\">{city.medianPrice}</p>\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  <TrendingUp className=\"h-4 w-4 text-green-600\" />\n                  <span className=\"text-sm font-medium text-green-600\">{city.change}</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </Card>\n\n        {/* Market Summary */}\n        <div className=\"mt-12 text-center\">\n          <Card className=\"p-8 bg-blue-50 border-blue-200\">\n            <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Market Summary</h3>\n            <p className=\"text-gray-700 max-w-3xl mx-auto\">\n              The Collin County real estate market continues to show strong growth with increasing home values \n              and steady demand. With excellent schools, growing job opportunities, and family-friendly communities, \n              the area remains one of Texas&apos;s most desirable places to live.\n            </p>\n          </Card>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,aAAa;IACjB;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,qNAAA,CAAA,aAAU;QAChB,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,sMAAA,CAAA,OAAI;QACV,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;IACf;CACD;AAED,MAAM,YAAY;IAChB;QACE,MAAM;QACN,aAAa;QACb,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,QAAQ;QACR,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,mIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,CAAC,iBAAiB,EAChC,KAAK,KAAK,KAAK,OAAO,iBAAiB,cACvC;kDACA,cAAA,6LAAC,KAAK,IAAI;4CAAC,WAAW,CAAC,QAAQ,EAC7B,KAAK,KAAK,KAAK,OAAO,mBAAmB,gBACzC;;;;;;;;;;;;;;;;8CAGN,6LAAC;oCAAG,WAAU;8CAA0C,KAAK,KAAK;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAyC,KAAK,KAAK;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,KAAK,KAAK,qBACd,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;iEAEtB,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDAE1B,6LAAC;4CAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,KAAK,KAAK,OAAO,mBAAmB,gBACzC;sDACC,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAK,WAAU;sDAAyB,KAAK,WAAW;;;;;;;;;;;;;2BAvBlD;;;;;;;;;;8BA8Bf,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B,KAAK,IAAI;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAoC,KAAK,WAAW;;;;;;;;;;;;sDAEnE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;8DAAsC,KAAK,MAAM;;;;;;;;;;;;;mCAP3D;;;;;;;;;;;;;;;;8BAehB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D;KAhFgB", "debugId": null}}, {"offset": {"line": 2255, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { Card } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  Users, \n  Award, \n  Clock, \n  Shield, \n  TrendingUp, \n  Heart,\n  Phone,\n  CheckCircle\n} from 'lucide-react'\n\nconst features = [\n  {\n    icon: Users,\n    title: 'Local Expertise',\n    description: 'Born and raised in Collin County, we know every neighborhood, school district, and community inside and out.',\n    stats: '25+ Years Local Experience'\n  },\n  {\n    icon: Award,\n    title: 'Proven Track Record',\n    description: 'Top-rated real estate team with hundreds of successful transactions and satisfied clients.',\n    stats: '2,500+ Homes Sold'\n  },\n  {\n    icon: Clock,\n    title: 'Fast Response Time',\n    description: 'We respond to inquiries within minutes, not hours. Your time is valuable, and we respect that.',\n    stats: 'Average 5-Minute Response'\n  },\n  {\n    icon: Shield,\n    title: 'Full-Service Support',\n    description: 'From initial search to closing day, we handle every detail to ensure a smooth transaction.',\n    stats: 'End-to-End Service'\n  },\n  {\n    icon: TrendingUp,\n    title: 'Market Insights',\n    description: 'Access to exclusive market data and trends to help you make informed decisions.',\n    stats: 'Real-Time Market Data'\n  },\n  {\n    icon: Heart,\n    title: 'Client-Focused',\n    description: 'Your satisfaction is our priority. We go above and beyond to exceed your expectations.',\n    stats: '98% Client Satisfaction'\n  }\n]\n\nconst testimonialHighlights = [\n  {\n    quote: \"Made our home buying process seamless and stress-free.\",\n    author: \"Sarah & <PERSON>\",\n    location: \"Plano\"\n  },\n  {\n    quote: \"Sold our home in just 5 days above asking price!\",\n    author: \"David Chen\",\n    location: \"Frisco\"\n  },\n  {\n    quote: \"Incredible knowledge of the local market and schools.\",\n    author: \"Jennifer Martinez\",\n    location: \"McKinney\"\n  }\n]\n\nexport function WhyChooseUs() {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Why Choose CollinCounty.homes?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Experience the difference of working with Collin County&apos;s most trusted real estate professionals\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {features.map((feature, index) => (\n            <Card key={index} className=\"p-6 text-center hover:shadow-lg transition-shadow\">\n              <div className=\"flex justify-center mb-4\">\n                <div className=\"p-3 bg-blue-100 rounded-full\">\n                  <feature.icon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{feature.title}</h3>\n              <p className=\"text-gray-600 mb-4\">{feature.description}</p>\n              <div className=\"text-sm font-medium text-blue-600\">{feature.stats}</div>\n            </Card>\n          ))}\n        </div>\n\n        {/* Testimonial Highlights */}\n        <div className=\"bg-white rounded-2xl p-8 mb-12\">\n          <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-8\">\n            What Our Clients Say\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonialHighlights.map((testimonial, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"flex justify-center mb-4\">\n                  <CheckCircle className=\"h-8 w-8 text-green-500\" />\n                </div>\n                <blockquote className=\"text-gray-700 italic mb-3\">\n                  &quot;{testimonial.quote}&quot;\n                </blockquote>\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-900\">{testimonial.author}</div>\n                  <div className=\"text-gray-500\">{testimonial.location}</div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <Card className=\"p-8 bg-blue-600 text-white text-center\">\n          <h3 className=\"text-2xl font-bold mb-4\">Ready to Get Started?</h3>\n          <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n            Whether you&apos;re buying your first home or selling your current one, \n            we&apos;re here to guide you through every step of the process.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\" variant=\"secondary\" className=\"px-8\">\n              <Phone className=\"h-5 w-5 mr-2\" />\n              Call (*************\n            </Button>\n            <Button size=\"lg\" variant=\"outline\" className=\"px-8 border-white text-white hover:bg-white hover:text-blue-600\">\n              Schedule Consultation\n            </Button>\n          </div>\n        </Card>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAeA,MAAM,WAAW;IACf;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAED,MAAM,wBAAwB;IAC5B;QACE,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,mIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG5B,6LAAC;oCAAG,WAAU;8CAA4C,QAAQ,KAAK;;;;;;8CACvE,6LAAC;oCAAE,WAAU;8CAAsB,QAAQ,WAAW;;;;;;8CACtD,6LAAC;oCAAI,WAAU;8CAAqC,QAAQ,KAAK;;;;;;;2BARxD;;;;;;;;;;8BAcf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,aAAa,sBACvC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,6LAAC;4CAAW,WAAU;;gDAA4B;gDACzC,YAAY,KAAK;gDAAC;;;;;;;sDAE3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA6B,YAAY,MAAM;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAAiB,YAAY,QAAQ;;;;;;;;;;;;;mCAT9C;;;;;;;;;;;;;;;;8BAiBhB,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAY,WAAU;;sDAC9C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,WAAU;8CAAkE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5H;KAxEgB", "debugId": null}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react'\n\nconst testimonials = [\n  {\n    id: 1,\n    name: '<PERSON> & <PERSON>',\n    location: 'Plano, TX',\n    rating: 5,\n    text: 'Working with the CollinCounty.homes team was an absolute pleasure. They helped us find our dream home in West Plano and made the entire process seamless. Their knowledge of the local market and school districts was invaluable.',\n    property: 'Purchased 4BR/3BA in West Plano',\n    date: 'November 2023'\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    location: 'Frisco, TX',\n    rating: 5,\n    text: 'I needed to sell my home quickly due to a job relocation. The team not only sold my house in just 5 days but got me $15,000 above asking price! Their marketing strategy and negotiation skills are top-notch.',\n    property: 'Sold 3BR/2BA in Frisco Square',\n    date: 'October 2023'\n  },\n  {\n    id: 3,\n    name: '<PERSON>',\n    location: 'McKinney, TX',\n    rating: 5,\n    text: 'As a first-time homebuyer, I was nervous about the process. The team walked me through every step, answered all my questions, and helped me find the perfect starter home in a great neighborhood.',\n    property: 'Purchased 3BR/2BA in Historic McKinney',\n    date: 'December 2023'\n  },\n  {\n    id: 4,\n    name: '<PERSON> & Lisa <PERSON>',\n    location: 'Allen, <PERSON>',\n    rating: 5,\n    text: 'We had been looking for a home for months with another agent with no success. Within two weeks of working with CollinCounty.homes, we found and closed on our perfect family home. Highly recommended!',\n    property: 'Purchased 5BR/4BA in Allen Station',\n    date: 'September 2023'\n  },\n  {\n    id: 5,\n    name: 'Amanda <PERSON>',\n    location: 'Prosper, TX',\n    rating: 5,\n    text: 'The team helped us navigate the competitive Prosper market and secure our dream home. Their responsiveness and attention to detail made all the difference in a fast-moving market.',\n    property: 'Purchased 4BR/3.5BA in Prosper Commons',\n    date: 'January 2024'\n  },\n  {\n    id: 6,\n    name: 'Mark Williams',\n    location: 'Richardson, TX',\n    rating: 5,\n    text: 'Professional, knowledgeable, and genuinely caring. They went above and beyond to help us sell our home and find a new one that better fit our growing family. Couldn\\'t be happier with the service.',\n    property: 'Sold & Purchased in Richardson Heights',\n    date: 'August 2023'\n  }\n]\n\nexport function TestimonialsSection() {\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const testimonialsPerPage = 3\n\n  const nextTestimonials = () => {\n    setCurrentIndex((prev) => \n      prev + testimonialsPerPage >= testimonials.length ? 0 : prev + testimonialsPerPage\n    )\n  }\n\n  const prevTestimonials = () => {\n    setCurrentIndex((prev) => \n      prev === 0 ? Math.max(0, testimonials.length - testimonialsPerPage) : prev - testimonialsPerPage\n    )\n  }\n\n  const currentTestimonials = testimonials.slice(currentIndex, currentIndex + testimonialsPerPage)\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Client Success Stories\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Don&apos;t just take our word for it. Here&apos;s what our satisfied clients have to say about their experience\n          </p>\n        </div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8\">\n          {currentTestimonials.map((testimonial) => (\n            <Card key={testimonial.id} className=\"p-6 hover:shadow-lg transition-shadow\">\n              {/* Rating */}\n              <div className=\"flex items-center mb-4\">\n                {[...Array(testimonial.rating)].map((_, i) => (\n                  <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n\n              {/* Quote */}\n              <div className=\"relative mb-6\">\n                <Quote className=\"absolute -top-2 -left-2 h-8 w-8 text-blue-200\" />\n                <p className=\"text-gray-700 italic pl-6\">{testimonial.text}</p>\n              </div>\n\n              {/* Client Info */}\n              <div className=\"border-t pt-4\">\n                <div className=\"font-semibold text-gray-900\">{testimonial.name}</div>\n                <div className=\"text-sm text-gray-600\">{testimonial.location}</div>\n                <div className=\"text-sm text-blue-600 mt-1\">{testimonial.property}</div>\n                <div className=\"text-xs text-gray-500 mt-1\">{testimonial.date}</div>\n              </div>\n            </Card>\n          ))}\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex justify-center items-center space-x-4\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={prevTestimonials}\n            disabled={currentIndex === 0}\n          >\n            <ChevronLeft className=\"h-4 w-4\" />\n          </Button>\n          \n          <div className=\"flex space-x-2\">\n            {Array.from({ length: Math.ceil(testimonials.length / testimonialsPerPage) }).map((_, i) => (\n              <button\n                key={i}\n                onClick={() => setCurrentIndex(i * testimonialsPerPage)}\n                className={`w-2 h-2 rounded-full transition-colors ${\n                  Math.floor(currentIndex / testimonialsPerPage) === i\n                    ? 'bg-blue-600'\n                    : 'bg-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={nextTestimonials}\n            disabled={currentIndex + testimonialsPerPage >= testimonials.length}\n          >\n            <ChevronRight className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-12\">\n          <Card className=\"p-8 bg-gray-50\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Ready to Join Our Success Stories?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Let us help you achieve your real estate goals. Contact us today for a free consultation.\n            </p>\n            <Button size=\"lg\" className=\"px-8\">\n              Get Started Today\n            </Button>\n          </Card>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,sBAAsB;IAE5B,MAAM,mBAAmB;QACvB,gBAAgB,CAAC,OACf,OAAO,uBAAuB,aAAa,MAAM,GAAG,IAAI,OAAO;IAEnE;IAEA,MAAM,mBAAmB;QACvB,gBAAgB,CAAC,OACf,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,aAAa,MAAM,GAAG,uBAAuB,OAAO;IAEjF;IAEA,MAAM,sBAAsB,aAAa,KAAK,CAAC,cAAc,eAAe;IAE5E,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,oBAAoB,GAAG,CAAC,CAAC,4BACxB,6LAAC,mIAAA,CAAA,OAAI;4BAAsB,WAAU;;8CAEnC,6LAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,6LAAC,qMAAA,CAAA,OAAI;4CAAS,WAAU;2CAAb;;;;;;;;;;8CAKf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAE,WAAU;sDAA6B,YAAY,IAAI;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA+B,YAAY,IAAI;;;;;;sDAC9D,6LAAC;4CAAI,WAAU;sDAAyB,YAAY,QAAQ;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDAA8B,YAAY,QAAQ;;;;;;sDACjE,6LAAC;4CAAI,WAAU;sDAA8B,YAAY,IAAI;;;;;;;;;;;;;2BAnBtD,YAAY,EAAE;;;;;;;;;;8BA0B7B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,iBAAiB;sCAE3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAGzB,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;4BAAqB,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpF,6LAAC;oCAEC,SAAS,IAAM,gBAAgB,IAAI;oCACnC,WAAW,CAAC,uCAAuC,EACjD,KAAK,KAAK,CAAC,eAAe,yBAAyB,IAC/C,gBACA,eACJ;mCANG;;;;;;;;;;sCAWX,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,eAAe,uBAAuB,aAAa,MAAM;sCAEnE,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GA9GgB;KAAA", "debugId": null}}]}