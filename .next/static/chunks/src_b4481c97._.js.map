{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(price)\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-slate-900 text-slate-50 hover:bg-slate-900/90\": variant === \"default\",\n            \"bg-red-500 text-slate-50 hover:bg-red-500/90\": variant === \"destructive\",\n            \"border border-slate-200 bg-white hover:bg-slate-100 hover:text-slate-900\": variant === \"outline\",\n            \"bg-slate-100 text-slate-900 hover:bg-slate-100/80\": variant === \"secondary\",\n            \"hover:bg-slate-100 hover:text-slate-900\": variant === \"ghost\",\n            \"text-slate-900 underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,oDAAoD,YAAY;YAChE,gDAAgD,YAAY;YAC5D,4EAA4E,YAAY;YACxF,qDAAqD,YAAY;YACjE,2CAA2C,YAAY;YACvD,qDAAqD,YAAY;QACnE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-slate-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wVACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport interface User {\n  id: string\n  email: string\n  name: string\n  role: 'buyer' | 'agent' | 'admin'\n  phone?: string\n  preferences?: {\n    savedSearches: any[]\n    favoriteProperties: string[]\n    notifications: boolean\n  }\n}\n\nexport interface AuthState {\n  user: User | null\n  loading: boolean\n  error: string | null\n}\n\nexport const authService = {\n  async signUp(email: string, password: string, userData: { name: string; role: 'buyer' | 'agent'; phone?: string }) {\n    try {\n      // Sign up with Supabase Auth\n      const { data: authData, error: authError } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            name: userData.name,\n            role: userData.role\n          }\n        }\n      })\n\n      if (authError) throw authError\n\n      // Create user profile in our users table\n      if (authData.user) {\n        const { error: profileError } = await supabase\n          .from('users')\n          .insert([\n            {\n              id: authData.user.id,\n              email: authData.user.email,\n              name: userData.name,\n              role: userData.role,\n              phone: userData.phone,\n              preferences: {\n                savedSearches: [],\n                favoriteProperties: [],\n                notifications: true\n              }\n            }\n          ])\n\n        if (profileError) throw profileError\n      }\n\n      return { data: authData, error: null }\n    } catch (error: any) {\n      return { data: null, error: error.message }\n    }\n  },\n\n  async signIn(email: string, password: string) {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      })\n\n      if (error) throw error\n\n      return { data, error: null }\n    } catch (error: any) {\n      return { data: null, error: error.message }\n    }\n  },\n\n  async signOut() {\n    try {\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      return { error: null }\n    } catch (error: any) {\n      return { error: error.message }\n    }\n  },\n\n  async getCurrentUser(): Promise<User | null> {\n    try {\n      const { data: { user: authUser } } = await supabase.auth.getUser()\n      \n      if (!authUser) return null\n\n      const { data: profile, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', authUser.id)\n        .single()\n\n      if (error) throw error\n\n      return {\n        id: profile.id,\n        email: profile.email,\n        name: profile.name,\n        role: profile.role,\n        phone: profile.phone,\n        preferences: profile.preferences\n      }\n    } catch (error) {\n      console.error('Error getting current user:', error)\n      return null\n    }\n  },\n\n  async updateProfile(userId: string, updates: Partial<User>) {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .update(updates)\n        .eq('id', userId)\n        .select()\n        .single()\n\n      if (error) throw error\n\n      return { data, error: null }\n    } catch (error: any) {\n      return { data: null, error: error.message }\n    }\n  },\n\n  async resetPassword(email: string) {\n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`\n      })\n\n      if (error) throw error\n\n      return { error: null }\n    } catch (error: any) {\n      return { error: error.message }\n    }\n  },\n\n  async updatePassword(newPassword: string) {\n    try {\n      const { error } = await supabase.auth.updateUser({\n        password: newPassword\n      })\n\n      if (error) throw error\n\n      return { error: null }\n    } catch (error: any) {\n      return { error: error.message }\n    }\n  },\n\n  // Favorite properties\n  async addToFavorites(propertyId: string) {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('Not authenticated')\n\n      const { error } = await supabase\n        .from('favorite_properties')\n        .insert([{ user_id: user.id, property_id: propertyId }])\n\n      if (error) throw error\n\n      return { error: null }\n    } catch (error: any) {\n      return { error: error.message }\n    }\n  },\n\n  async removeFromFavorites(propertyId: string) {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('Not authenticated')\n\n      const { error } = await supabase\n        .from('favorite_properties')\n        .delete()\n        .eq('user_id', user.id)\n        .eq('property_id', propertyId)\n\n      if (error) throw error\n\n      return { error: null }\n    } catch (error: any) {\n      return { error: error.message }\n    }\n  },\n\n  async getFavorites() {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('Not authenticated')\n\n      const { data, error } = await supabase\n        .from('favorite_properties')\n        .select('property_id, properties(*)')\n        .eq('user_id', user.id)\n\n      if (error) throw error\n\n      return { data: data?.map(item => item.properties) || [], error: null }\n    } catch (error: any) {\n      return { data: [], error: error.message }\n    }\n  },\n\n  // Saved searches\n  async saveSearch(name: string, filters: any) {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('Not authenticated')\n\n      const { data, error } = await supabase\n        .from('saved_searches')\n        .insert([{\n          user_id: user.id,\n          name,\n          filters\n        }])\n        .select()\n        .single()\n\n      if (error) throw error\n\n      return { data, error: null }\n    } catch (error: any) {\n      return { data: null, error: error.message }\n    }\n  },\n\n  async getSavedSearches() {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('Not authenticated')\n\n      const { data, error } = await supabase\n        .from('saved_searches')\n        .select('*')\n        .eq('user_id', user.id)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      return { data: data || [], error: null }\n    } catch (error: any) {\n      return { data: [], error: error.message }\n    }\n  },\n\n  async deleteSavedSearch(searchId: string) {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('Not authenticated')\n\n      const { error } = await supabase\n        .from('saved_searches')\n        .delete()\n        .eq('id', searchId)\n        .eq('user_id', user.id)\n\n      if (error) throw error\n\n      return { error: null }\n    } catch (error: any) {\n      return { error: error.message }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;AACxD,MAAM,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;AAE1D,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAqB3C,MAAM,cAAc;IACzB,MAAM,QAAO,KAAa,EAAE,QAAgB,EAAE,QAAmE;QAC/G,IAAI;YACF,6BAA6B;YAC7B,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBACtE;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,MAAM,SAAS,IAAI;wBACnB,MAAM,SAAS,IAAI;oBACrB;gBACF;YACF;YAEA,IAAI,WAAW,MAAM;YAErB,yCAAyC;YACzC,IAAI,SAAS,IAAI,EAAE;gBACjB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN;wBACE,IAAI,SAAS,IAAI,CAAC,EAAE;wBACpB,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,MAAM,SAAS,IAAI;wBACnB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,aAAa;4BACX,eAAe,EAAE;4BACjB,oBAAoB,EAAE;4BACtB,eAAe;wBACjB;oBACF;iBACD;gBAEH,IAAI,cAAc,MAAM;YAC1B;YAEA,OAAO;gBAAE,MAAM;gBAAU,OAAO;YAAK;QACvC,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;IACF;IAEA,MAAM,QAAO,KAAa,EAAE,QAAgB;QAC1C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE;gBAAM,OAAO;YAAK;QAC7B,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;IACF;IAEA,MAAM;QACJ,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAChC;IACF;IAEA,MAAM;QACJ,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,MAAM,QAAQ,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAEhE,IAAI,CAAC,UAAU,OAAO;YAEtB,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBACL,IAAI,QAAQ,EAAE;gBACd,OAAO,QAAQ,KAAK;gBACpB,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI;gBAClB,OAAO,QAAQ,KAAK;gBACpB,aAAa,QAAQ,WAAW;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,MAAM,eAAc,MAAc,EAAE,OAAsB;QACxD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE;gBAAM,OAAO;YAAK;QAC7B,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;IACF;IAEA,MAAM,eAAc,KAAa;QAC/B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAChC;IACF;IAEA,MAAM,gBAAe,WAAmB;QACtC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;gBAC/C,UAAU;YACZ;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAChC;IACF;IAEA,sBAAsB;IACtB,MAAM,gBAAe,UAAkB;QACrC,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC;gBAAC;oBAAE,SAAS,KAAK,EAAE;oBAAE,aAAa;gBAAW;aAAE;YAEzD,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAChC;IACF;IAEA,MAAM,qBAAoB,UAAkB;QAC1C,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,GACN,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,EAAE,CAAC,eAAe;YAErB,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAChC;IACF;IAEA,MAAM;QACJ,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,uBACL,MAAM,CAAC,8BACP,EAAE,CAAC,WAAW,KAAK,EAAE;YAExB,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,MAAM,MAAM,IAAI,CAAA,OAAQ,KAAK,UAAU,KAAK,EAAE;gBAAE,OAAO;YAAK;QACvE,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC1C;IACF;IAEA,iBAAiB;IACjB,MAAM,YAAW,IAAY,EAAE,OAAY;QACzC,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;gBAAC;oBACP,SAAS,KAAK,EAAE;oBAChB;oBACA;gBACF;aAAE,EACD,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE;gBAAM,OAAO;YAAK;QAC7B,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;IACF;IAEA,MAAM;QACJ,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,MAAM,QAAQ,EAAE;gBAAE,OAAO;YAAK;QACzC,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC1C;IACF;IAEA,MAAM,mBAAkB,QAAgB;QACtC,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM,UACT,EAAE,CAAC,WAAW,KAAK,EAAE;YAExB,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAChC;IACF;AACF", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react'\nimport { User, AuthState, authService, supabase } from '@/lib/auth'\n\ninterface AuthContextType extends AuthState {\n  signUp: (email: string, password: string, userData: { name: string; role: 'buyer' | 'agent'; phone?: string }) => Promise<{ error: string | null }>\n  signIn: (email: string, password: string) => Promise<{ error: string | null }>\n  signOut: () => Promise<{ error: string | null }>\n  updateProfile: (updates: Partial<User>) => Promise<{ error: string | null }>\n  resetPassword: (email: string) => Promise<{ error: string | null }>\n  updatePassword: (newPassword: string) => Promise<{ error: string | null }>\n  addToFavorites: (propertyId: string) => Promise<{ error: string | null }>\n  removeFromFavorites: (propertyId: string) => Promise<{ error: string | null }>\n  getFavorites: () => Promise<{ data: any[]; error: string | null }>\n  saveSearch: (name: string, filters: any) => Promise<{ data: any; error: string | null }>\n  getSavedSearches: () => Promise<{ data: any[]; error: string | null }>\n  deleteSavedSearch: (searchId: string) => Promise<{ error: string | null }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: ReactNode }) {\n  const [state, setState] = useState<AuthState>({\n    user: null,\n    loading: true,\n    error: null\n  })\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const user = await authService.getCurrentUser()\n        setState(prev => ({ ...prev, user, loading: false }))\n      } catch (error) {\n        setState(prev => ({ ...prev, loading: false, error: 'Failed to load user session' }))\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          const user = await authService.getCurrentUser()\n          setState(prev => ({ ...prev, user, loading: false, error: null }))\n        } else if (event === 'SIGNED_OUT') {\n          setState(prev => ({ ...prev, user: null, loading: false, error: null }))\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signUp = async (email: string, password: string, userData: { name: string; role: 'buyer' | 'agent'; phone?: string }) => {\n    setState(prev => ({ ...prev, loading: true, error: null }))\n    \n    const { error } = await authService.signUp(email, password, userData)\n    \n    if (error) {\n      setState(prev => ({ ...prev, loading: false, error }))\n      return { error }\n    }\n\n    setState(prev => ({ ...prev, loading: false }))\n    return { error: null }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    setState(prev => ({ ...prev, loading: true, error: null }))\n    \n    const { error } = await authService.signIn(email, password)\n    \n    if (error) {\n      setState(prev => ({ ...prev, loading: false, error }))\n      return { error }\n    }\n\n    // User will be set by the auth state change listener\n    return { error: null }\n  }\n\n  const signOut = async () => {\n    setState(prev => ({ ...prev, loading: true, error: null }))\n    \n    const { error } = await authService.signOut()\n    \n    if (error) {\n      setState(prev => ({ ...prev, loading: false, error }))\n      return { error }\n    }\n\n    // User will be cleared by the auth state change listener\n    return { error: null }\n  }\n\n  const updateProfile = async (updates: Partial<User>) => {\n    if (!state.user) return { error: 'Not authenticated' }\n\n    setState(prev => ({ ...prev, loading: true, error: null }))\n    \n    const { data, error } = await authService.updateProfile(state.user.id, updates)\n    \n    if (error) {\n      setState(prev => ({ ...prev, loading: false, error }))\n      return { error }\n    }\n\n    setState(prev => ({ \n      ...prev, \n      user: data ? { ...prev.user!, ...data } : prev.user,\n      loading: false \n    }))\n    \n    return { error: null }\n  }\n\n  const resetPassword = async (email: string) => {\n    return await authService.resetPassword(email)\n  }\n\n  const updatePassword = async (newPassword: string) => {\n    return await authService.updatePassword(newPassword)\n  }\n\n  const addToFavorites = async (propertyId: string) => {\n    return await authService.addToFavorites(propertyId)\n  }\n\n  const removeFromFavorites = async (propertyId: string) => {\n    return await authService.removeFromFavorites(propertyId)\n  }\n\n  const getFavorites = async () => {\n    return await authService.getFavorites()\n  }\n\n  const saveSearch = async (name: string, filters: any) => {\n    return await authService.saveSearch(name, filters)\n  }\n\n  const getSavedSearches = async () => {\n    return await authService.getSavedSearches()\n  }\n\n  const deleteSavedSearch = async (searchId: string) => {\n    return await authService.deleteSavedSearch(searchId)\n  }\n\n  const value: AuthContextType = {\n    ...state,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    resetPassword,\n    updatePassword,\n    addToFavorites,\n    removeFromFavorites,\n    getFavorites,\n    saveSearch,\n    getSavedSearches,\n    deleteSavedSearch\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAoBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAC5C,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,IAAI;wBACF,MAAM,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,cAAc;wBAC7C;wEAAS,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE;oCAAM,SAAS;gCAAM,CAAC;;oBACrD,EAAE,OAAO,OAAO;wBACd;wEAAS,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,SAAS;oCAAO,OAAO;gCAA8B,CAAC;;oBACrF;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,qHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,IAAI,UAAU,eAAe,SAAS,MAAM;wBAC1C,MAAM,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,cAAc;wBAC7C;sDAAS,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE;oCAAM,SAAS;oCAAO,OAAO;gCAAK,CAAC;;oBAClE,OAAO,IAAI,UAAU,cAAc;wBACjC;sDAAS,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM;oCAAM,SAAS;oCAAO,OAAO;gCAAK,CAAC;;oBACxE;gBACF;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,OAAO,UAAU;QAE5D,IAAI,OAAO;YACT,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO;gBAAM,CAAC;YACpD,OAAO;gBAAE;YAAM;QACjB;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAM,CAAC;QAC7C,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,OAAO;QAElD,IAAI,OAAO;YACT,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO;gBAAM,CAAC;YACpD,OAAO;gBAAE;YAAM;QACjB;QAEA,qDAAqD;QACrD,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,MAAM,UAAU;QACd,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;QAE3C,IAAI,OAAO;YACT,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO;gBAAM,CAAC;YACpD,OAAO;gBAAE;YAAM;QACjB;QAEA,yDAAyD;QACzD,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;YAAE,OAAO;QAAoB;QAErD,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qHAAA,CAAA,cAAW,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE;QAEvE,IAAI,OAAO;YACT,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO;gBAAM,CAAC;YACpD,OAAO;gBAAE;YAAM;QACjB;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,MAAM,OAAO;oBAAE,GAAG,KAAK,IAAI;oBAAG,GAAG,IAAI;gBAAC,IAAI,KAAK,IAAI;gBACnD,SAAS;YACX,CAAC;QAED,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,aAAa,CAAC;IACzC;IAEA,MAAM,iBAAiB,OAAO;QAC5B,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;IAC1C;IAEA,MAAM,iBAAiB,OAAO;QAC5B,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;IAC1C;IAEA,MAAM,sBAAsB,OAAO;QACjC,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,mBAAmB,CAAC;IAC/C;IAEA,MAAM,eAAe;QACnB,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,YAAY;IACvC;IAEA,MAAM,aAAa,OAAO,MAAc;QACtC,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,MAAM;IAC5C;IAEA,MAAM,mBAAmB;QACvB,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,gBAAgB;IAC3C;IAEA,MAAM,oBAAoB,OAAO;QAC/B,OAAO,MAAM,qHAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC;IAC7C;IAEA,MAAM,QAAyB;QAC7B,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAvJgB;KAAA;AAyJT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/CollinCountyhomes/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Search, Menu, X, Phone, Mail, MapPin, User, LogOut } from 'lucide-react'\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [searchQuery, setSearchQuery] = useState('')\n  const [showUserMenu, setShowUserMenu] = useState(false)\n  const { user, signOut } = useAuth()\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Properties', href: '/properties' },\n    { name: 'Neighborhoods', href: '/neighborhoods' },\n    { name: 'Agents', href: '/agents' },\n    { name: 'About', href: '/about' },\n    { name: 'Contact', href: '/contact' },\n  ]\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      {/* Top Bar */}\n      <div className=\"bg-slate-900 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-10 text-sm\">\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"flex items-center space-x-2\">\n                <Phone className=\"h-3 w-3\" />\n                <span>(*************</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Mail className=\"h-3 w-3\" />\n                <span><EMAIL></span>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <MapPin className=\"h-3 w-3\" />\n              <span>Serving All of Collin County, Texas</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"text-2xl font-bold text-slate-900\">\n                Collin County\n                <span className=\"text-blue-600\">.homes</span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-slate-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Search Bar & Auth */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                type=\"text\"\n                placeholder=\"Search by city, address, or MLS#\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 w-64\"\n              />\n            </div>\n            <Button>Search</Button>\n\n            {/* Auth Section */}\n            {user ? (\n              <div className=\"relative\">\n                <Button\n                  variant=\"ghost\"\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"flex items-center space-x-2\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>{user.name}</span>\n                </Button>\n\n                {showUserMenu && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border\">\n                    <Link\n                      href=\"/dashboard\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      Dashboard\n                    </Link>\n                    <Link\n                      href=\"/dashboard/profile\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      Profile\n                    </Link>\n                    <Link\n                      href=\"/dashboard/favorites\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      Favorites\n                    </Link>\n                    {user.role === 'agent' && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        Agent Portal\n                      </Link>\n                    )}\n                    <hr className=\"my-1\" />\n                    <button\n                      onClick={() => {\n                        signOut()\n                        setShowUserMenu(false)\n                      }}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      <LogOut className=\"h-4 w-4 inline mr-2\" />\n                      Sign Out\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Button variant=\"ghost\" asChild>\n                  <Link href=\"/auth/login\">Sign In</Link>\n                </Button>\n                <Button asChild>\n                  <Link href=\"/auth/signup\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n            >\n              {isMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-slate-700 hover:text-blue-600 block px-3 py-2 text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n\n            {/* Mobile Search */}\n            <div className=\"px-3 py-2\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search properties...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10 w-full\"\n                />\n              </div>\n              <Button className=\"w-full mt-2\">Search</Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAiB,MAAM;QAAiB;QAChD;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;;wCAAoC;sDAEjD,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;sCAMtC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAGd,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;gCAGP,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,KAAK,IAAI;;;;;;;;;;;;wCAGjB,8BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB;8DAChC;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB;8DAChC;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB;8DAChC;;;;;;gDAGA,KAAK,IAAI,KAAK,yBACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB;8DAChC;;;;;;8DAIH,6LAAC;oDAAG,WAAU;;;;;;8DACd,6LAAC;oDACC,SAAS;wDACP;wDACA,gBAAgB;oDAClB;oDACA,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;;;;;;;;;;;;yDAOlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,OAAO;sDAC7B,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAc;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;;;;;;;;;;;;;sCAOlC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc,CAAC;0CAE7B,2BACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,IAAI;+BALL,KAAK,IAAI;;;;;sCAUlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAGd,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;GAxMgB;;QAIY,kIAAA,CAAA,UAAO;;;KAJnB", "debugId": null}}]}