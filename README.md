# Collin County Homes 🏡

A modern, full-featured real estate website for Collin County, Texas, built with cutting-edge web technologies. This platform provides comprehensive property listings, neighborhood guides, and real estate services for buyers, sellers, and agents.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/collin-county-homes)
[![CI/CD Pipeline](https://github.com/your-username/collin-county-homes/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/your-username/collin-county-homes/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## ✨ Features

### 🏠 Property Management
- **Advanced Property Search**: Filter by price, bedrooms, bathrooms, property type, and location
- **Interactive Property Listings**: High-quality images, detailed descriptions, and virtual tours
- **Property Details**: Comprehensive information including schools, neighborhood data, and market trends
- **Favorites System**: Save and manage favorite properties with user accounts
- **Property Comparison**: Side-by-side comparison of multiple properties

### 🗺️ Location Intelligence
- **Neighborhood Guides**: Detailed information about Collin County communities
- **School District Information**: Ratings and details for elementary, middle, and high schools
- **Local Amenities**: Parks, shopping, dining, and entertainment options
- **Commute Information**: Travel times to major employment centers
- **Market Analytics**: Price trends and neighborhood statistics

### 👥 User Experience
- **User Authentication**: Secure login/signup with Supabase Auth
- **Personal Dashboard**: Manage favorites, saved searches, and account settings
- **Agent Profiles**: Detailed agent information and contact options
- **Contact Forms**: Lead generation with property-specific inquiries
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### 🚀 Performance & SEO
- **Lightning Fast**: Optimized for Core Web Vitals and performance
- **SEO Optimized**: Structured data, meta tags, and sitemap generation
- **Analytics Integration**: Google Analytics, GTM, and Facebook Pixel
- **Accessibility**: WCAG compliant with screen reader support
- **Progressive Web App**: Offline capabilities and app-like experience

## 🛠️ Tech Stack

### Frontend
- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **Language**: [TypeScript](https://www.typescriptlang.org/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **UI Components**: Custom components with [Radix UI](https://www.radix-ui.com/)
- **Icons**: [Lucide React](https://lucide.dev/)

### Backend & Database
- **Database**: [Supabase](https://supabase.com/) (PostgreSQL)
- **Authentication**: Supabase Auth
- **Real-time**: Supabase Realtime subscriptions
- **File Storage**: Supabase Storage for images

### Development & Deployment
- **Package Manager**: npm
- **Testing**: [Jest](https://jestjs.io/) + [React Testing Library](https://testing-library.com/) + [Playwright](https://playwright.dev/)
- **Linting**: [ESLint](https://eslint.org/) + [Prettier](https://prettier.io/)
- **CI/CD**: GitHub Actions
- **Deployment**: [Vercel](https://vercel.com/)
- **Monitoring**: Lighthouse CI, Sentry (optional)

### Analytics & Marketing
- **Analytics**: Google Analytics 4
- **Tag Management**: Google Tag Manager
- **Social Media**: Facebook Pixel
- **SEO**: Structured data, sitemaps, robots.txt

## 🚀 Quick Start

### Prerequisites
- Node.js 18 or higher
- npm or yarn
- Git

### Installation

1. **Clone the repository**:
```bash
git clone https://github.com/your-username/collin-county-homes.git
cd collin-county-homes
```

2. **Install dependencies**:
```bash
npm install
```

3. **Set up environment variables**:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_ga_id
NEXT_PUBLIC_GTM_ID=your_gtm_id
NEXT_PUBLIC_FB_PIXEL_ID=your_fb_pixel_id
```

4. **Set up the database**:
```bash
# Run the database schema
psql -f database/schema.sql

# Seed initial data
psql -f database/seed.sql
```

5. **Run the development server**:
```bash
npm run dev
```

6. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
collin-county-homes/
├── 📁 src/
│   ├── 📁 app/                    # Next.js App Router pages
│   │   ├── 📁 (auth)/            # Authentication pages
│   │   ├── 📁 admin/             # Admin dashboard
│   │   ├── 📁 api/               # API routes
│   │   ├── 📁 dashboard/         # User dashboard
│   │   ├── 📁 neighborhoods/     # Neighborhood pages
│   │   ├── 📁 properties/        # Property pages
│   │   └── layout.tsx            # Root layout
│   ├── 📁 components/            # Reusable components
│   │   ├── 📁 properties/        # Property-specific components
│   │   ├── 📁 ui/               # Base UI components
│   │   └── ...
│   ├── 📁 contexts/              # React contexts
│   ├── 📁 hooks/                 # Custom React hooks
│   ├── 📁 lib/                   # Utility functions
│   └── 📁 types/                 # TypeScript definitions
├── 📁 database/                  # Database schema and seeds
├── 📁 e2e/                      # End-to-end tests
├── 📁 public/                   # Static assets
├── 📁 scripts/                  # Build and deployment scripts
├── 📁 .github/workflows/        # GitHub Actions
└── 📄 Configuration files
```

## 🧪 Testing

### Run All Tests
```bash
# Complete test suite
./scripts/test-setup.sh

# Individual test types
npm run test              # Unit tests
npm run test:e2e         # End-to-end tests
npm run test:coverage    # Coverage report
npm run lint             # Linting
npm run type-check       # TypeScript checking
```

### Test Coverage
- **Unit Tests**: Components, utilities, and business logic
- **Integration Tests**: API routes and database operations
- **E2E Tests**: Complete user workflows
- **Performance Tests**: Lighthouse CI integration
- **Security Tests**: Dependency auditing

## 🚀 Deployment

### Quick Deploy to Vercel
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/collin-county-homes)

### Manual Deployment
```bash
# Deploy to staging
./scripts/deploy.sh --environment staging

# Deploy to production
./scripts/deploy.sh --environment production
```

### Docker Deployment
```bash
# Build and run with Docker
docker-compose up -d
```

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## 📊 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized for LCP, FID, and CLS
- **Bundle Size**: Optimized with code splitting and tree shaking
- **Image Optimization**: Next.js Image component with WebP/AVIF support
- **Caching**: Aggressive caching strategies for static and dynamic content

## 🔒 Security

- **Authentication**: Secure JWT-based authentication with Supabase
- **Authorization**: Row Level Security (RLS) policies
- **Data Protection**: HTTPS everywhere, secure headers
- **Input Validation**: Server-side validation for all inputs
- **Security Headers**: CSP, HSTS, and other security headers
- **Dependency Scanning**: Automated vulnerability scanning

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass: `npm run test:all`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

### Code Standards
- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration with custom rules
- **Prettier**: Consistent code formatting
- **Conventional Commits**: Standardized commit messages
- **Testing**: Minimum 80% code coverage required

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js Team**: For the amazing React framework
- **Vercel**: For seamless deployment and hosting
- **Supabase**: For the backend-as-a-service platform
- **Tailwind CSS**: For the utility-first CSS framework
- **Open Source Community**: For the countless libraries and tools

## 📞 Support

- **Documentation**: [Full documentation](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/collin-county-homes/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/collin-county-homes/discussions)
- **Email**: <EMAIL>

---

<div align="center">
  <p>Built with ❤️ for the Collin County community</p>
  <p>
    <a href="https://collincounty.homes">Website</a> •
    <a href="DEPLOYMENT.md">Deployment Guide</a> •
    <a href="CONTRIBUTING.md">Contributing</a> •
    <a href="LICENSE">License</a>
  </p>
</div>
