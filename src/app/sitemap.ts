import { MetadataRoute } from 'next'

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://collincounty.homes'

// Mock data - in a real app, this would come from your database
const mockProperties = [
  { id: '1', slug: 'luxury-estate-west-plano', updatedAt: '2024-01-15' },
  { id: '2', slug: 'modern-townhome-frisco-square', updatedAt: '2024-01-14' },
  { id: '3', slug: 'family-home-historic-mckinney', updatedAt: '2024-01-13' },
  { id: '4', slug: 'new-construction-allen-station', updatedAt: '2024-01-12' },
  { id: '5', slug: 'luxury-condo-prosper-commons', updatedAt: '2024-01-11' },
]

const mockNeighborhoods = [
  { id: 'west-plano', slug: 'west-plano', updatedAt: '2024-01-10' },
  { id: 'frisco-square', slug: 'frisco-square', updatedAt: '2024-01-10' },
  { id: 'historic-mckinney', slug: 'historic-mckinney', updatedAt: '2024-01-10' },
  { id: 'allen-station', slug: 'allen-station', updatedAt: '2024-01-10' },
]

export default function sitemap(): MetadataRoute.Sitemap {
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/properties`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/neighborhoods`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/agents`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
  ]

  // Property pages
  const propertyPages = mockProperties.map((property) => ({
    url: `${baseUrl}/properties/${property.id}`,
    lastModified: new Date(property.updatedAt),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }))

  // Neighborhood pages
  const neighborhoodPages = mockNeighborhoods.map((neighborhood) => ({
    url: `${baseUrl}/neighborhoods/${neighborhood.slug}`,
    lastModified: new Date(neighborhood.updatedAt),
    changeFrequency: 'monthly' as const,
    priority: 0.7,
  }))

  // Search pages for popular cities
  const cityPages = [
    'plano',
    'frisco',
    'mckinney',
    'allen',
    'prosper',
    'richardson',
    'carrollton',
    'lewisville',
    'the-colony',
    'little-elm'
  ].map((city) => ({
    url: `${baseUrl}/properties?city=${city}`,
    lastModified: new Date(),
    changeFrequency: 'daily' as const,
    priority: 0.6,
  }))

  // Property type pages
  const propertyTypePages = [
    'house',
    'townhouse',
    'condo',
    'land'
  ].map((type) => ({
    url: `${baseUrl}/properties?type=${type}`,
    lastModified: new Date(),
    changeFrequency: 'daily' as const,
    priority: 0.5,
  }))

  return [
    ...staticPages,
    ...propertyPages,
    ...neighborhoodPages,
    ...cityPages,
    ...propertyTypePages,
  ]
}

// Alternative function to generate sitemap XML manually if needed
export function generateSitemapXML(): string {
  const sitemap = [
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">',
  ]

  // Add static pages
  const staticUrls = [
    { url: '', priority: '1.0', changefreq: 'daily' },
    { url: '/properties', priority: '0.9', changefreq: 'daily' },
    { url: '/neighborhoods', priority: '0.8', changefreq: 'weekly' },
    { url: '/about', priority: '0.7', changefreq: 'monthly' },
    { url: '/contact', priority: '0.7', changefreq: 'monthly' },
  ]

  staticUrls.forEach(({ url, priority, changefreq }) => {
    sitemap.push(`
  <url>
    <loc>${baseUrl}${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`)
  })

  // Add property pages
  mockProperties.forEach((property) => {
    sitemap.push(`
  <url>
    <loc>${baseUrl}/properties/${property.id}</loc>
    <lastmod>${new Date(property.updatedAt).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`)
  })

  // Add neighborhood pages
  mockNeighborhoods.forEach((neighborhood) => {
    sitemap.push(`
  <url>
    <loc>${baseUrl}/neighborhoods/${neighborhood.slug}</loc>
    <lastmod>${new Date(neighborhood.updatedAt).toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`)
  })

  sitemap.push('</urlset>')
  
  return sitemap.join('')
}
