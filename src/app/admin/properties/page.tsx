'use client'

import { useState } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  MoreHorizontal,
  Building
} from 'lucide-react'
import { formatPrice } from '@/lib/utils'

// Mock data - in a real app, this would come from the database
const mockProperties = [
  {
    id: '1',
    title: 'Luxury Estate in West Plano',
    price: 850000,
    address: '123 Oak Tree Lane, Plano, TX 75024',
    bedrooms: 5,
    bathrooms: 4.5,
    sqft: 4200,
    status: 'for-sale',
    agent: '<PERSON>',
    daysOnMarket: 12,
    createdAt: '2024-01-03'
  },
  {
    id: '2',
    title: 'Modern Townhome in Frisco',
    price: 425000,
    address: '456 Maple Street, Frisco, TX 75034',
    bedrooms: 3,
    bathrooms: 2.5,
    sqft: 2100,
    status: 'pending',
    agent: '<PERSON>',
    daysOnMarket: 8,
    createdAt: '2024-01-05'
  },
  {
    id: '3',
    title: 'Family Home in McKinney',
    price: 375000,
    address: '789 Pine Avenue, McKinney, TX 75070',
    bedrooms: 4,
    bathrooms: 3,
    sqft: 2800,
    status: 'for-sale',
    agent: 'Jennifer Martinez',
    daysOnMarket: 25,
    createdAt: '2023-12-20'
  },
  {
    id: '4',
    title: 'New Construction in Allen',
    price: 525000,
    address: '321 Cedar Drive, Allen, TX 75013',
    bedrooms: 4,
    bathrooms: 3.5,
    sqft: 3200,
    status: 'for-sale',
    agent: 'Sarah Johnson',
    daysOnMarket: 3,
    createdAt: '2024-01-12'
  },
  {
    id: '5',
    title: 'Luxury Condo in Prosper',
    price: 295000,
    address: '654 Willow Creek, Prosper, TX 75078',
    bedrooms: 2,
    bathrooms: 2,
    sqft: 1400,
    status: 'sold',
    agent: 'Michael Chen',
    daysOnMarket: 15,
    createdAt: '2023-12-15'
  }
]

export default function AdminPropertiesPage() {
  const [properties, setProperties] = useState(mockProperties)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  // Filter and sort properties
  const filteredProperties = properties
    .filter(property => {
      const matchesSearch = property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           property.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           property.agent.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === 'all' || property.status === statusFilter
      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-asc':
          return a.price - b.price
        case 'price-desc':
          return b.price - a.price
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'newest':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      }
    })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'for-sale':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'sold':
        return 'bg-green-100 text-green-800'
      case 'off-market':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
          <p className="text-gray-600 mt-2">Manage your property listings</p>
        </div>
        <Button asChild>
          <Link href="/admin/properties/new">
            <Plus className="h-4 w-4 mr-2" />
            Add Property
          </Link>
        </Button>
      </div>

      {/* Filters and Search */}
      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search properties, addresses, or agents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="for-sale">For Sale</option>
            <option value="pending">Pending</option>
            <option value="sold">Sold</option>
            <option value="off-market">Off Market</option>
          </select>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="price-desc">Price: High to Low</option>
            <option value="price-asc">Price: Low to High</option>
          </select>
        </div>
      </Card>

      {/* Properties List */}
      <Card>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Property</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Price</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Details</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Agent</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Days on Market</th>
                <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredProperties.map((property) => (
                <tr key={property.id} className="hover:bg-gray-50">
                  <td className="py-4 px-4">
                    <div>
                      <h3 className="font-medium text-gray-900">{property.title}</h3>
                      <p className="text-sm text-gray-600">{property.address}</p>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <span className="font-semibold text-green-600">
                      {formatPrice(property.price)}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <div className="text-sm text-gray-600">
                      <div>{property.bedrooms} bed, {property.bathrooms} bath</div>
                      <div>{property.sqft?.toLocaleString()} sqft</div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <Badge className={getStatusColor(property.status)}>
                      {property.status.replace('-', ' ')}
                    </Badge>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-sm text-gray-900">{property.agent}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-sm text-gray-600">{property.daysOnMarket} days</span>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center justify-end space-x-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/properties/${property.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/admin/properties/${property.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredProperties.length === 0 && (
          <div className="text-center py-12">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || statusFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first property'
              }
            </p>
            <Button asChild>
              <Link href="/admin/properties/new">
                <Plus className="h-4 w-4 mr-2" />
                Add Property
              </Link>
            </Button>
          </div>
        )}
      </Card>
    </div>
  )
}
