'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  Mail, 
  Phone, 
  MessageSquare,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react'

// Mock data - in a real app, this would come from the database
const mockContacts = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    subject: 'Property Viewing',
    message: 'I am interested in scheduling a showing for the luxury estate in West Plano. Please contact me at your earliest convenience.',
    propertyInterest: '123 Oak Tree Lane, Plano, TX',
    preferredContact: 'phone',
    status: 'new',
    createdAt: '2024-01-15T10:30:00Z',
    agent: null
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    subject: 'Buying a Home',
    message: 'This property looks perfect for our family. Can you provide more information about the school district and neighborhood amenities?',
    propertyInterest: '789 Pine Avenue, McKinney, TX',
    preferredContact: 'email',
    status: 'contacted',
    createdAt: '2024-01-14T14:20:00Z',
    agent: 'Jennifer Martinez'
  },
  {
    id: '3',
    name: 'David Brown',
    email: '<EMAIL>',
    phone: '(*************',
    subject: 'General Inquiry',
    message: 'I would like to know more about the HOA fees and community amenities for the townhome in Frisco Square.',
    propertyInterest: '456 Maple Street, Frisco, TX',
    preferredContact: 'email',
    status: 'closed',
    createdAt: '2024-01-13T09:15:00Z',
    agent: 'Michael Chen'
  },
  {
    id: '4',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    subject: 'Selling a Home',
    message: 'I am interested in selling my current home and would like a market analysis. When would be a good time to schedule a consultation?',
    propertyInterest: '',
    preferredContact: 'phone',
    status: 'new',
    createdAt: '2024-01-12T16:45:00Z',
    agent: null
  },
  {
    id: '5',
    name: 'Michael Davis',
    email: '<EMAIL>',
    phone: '(*************',
    subject: 'Investment Properties',
    message: 'Looking for investment opportunities in Collin County. Do you have any properties that would be good rental investments?',
    propertyInterest: '',
    preferredContact: 'email',
    status: 'contacted',
    createdAt: '2024-01-11T11:30:00Z',
    agent: 'Sarah Johnson'
  }
]

export default function AdminContactsPage() {
  const [contacts, setContacts] = useState(mockContacts)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  // Filter and sort contacts
  const filteredContacts = contacts
    .filter(contact => {
      const matchesSearch = contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           contact.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           contact.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           contact.message.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === 'all' || contact.status === statusFilter
      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'name':
          return a.name.localeCompare(b.name)
        case 'newest':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      }
    })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-red-100 text-red-800'
      case 'contacted':
        return 'bg-yellow-100 text-yellow-800'
      case 'closed':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return AlertCircle
      case 'contacted':
        return Clock
      case 'closed':
        return CheckCircle
      default:
        return MessageSquare
    }
  }

  const updateContactStatus = (contactId: string, newStatus: string) => {
    setContacts(prev => prev.map(contact => 
      contact.id === contactId 
        ? { ...contact, status: newStatus }
        : contact
    ))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const statusCounts = {
    all: contacts.length,
    new: contacts.filter(c => c.status === 'new').length,
    contacted: contacts.filter(c => c.status === 'contacted').length,
    closed: contacts.filter(c => c.status === 'closed').length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Contact Management</h1>
          <p className="text-gray-600 mt-2">Manage and respond to customer inquiries</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageSquare className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-2xl font-bold text-gray-900">{statusCounts.all}</h3>
              <p className="text-gray-600">Total Contacts</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-2xl font-bold text-gray-900">{statusCounts.new}</h3>
              <p className="text-gray-600">New Inquiries</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-2xl font-bold text-gray-900">{statusCounts.contacted}</h3>
              <p className="text-gray-600">In Progress</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-2xl font-bold text-gray-900">{statusCounts.closed}</h3>
              <p className="text-gray-600">Closed</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search contacts, emails, or messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="new">New</option>
            <option value="contacted">Contacted</option>
            <option value="closed">Closed</option>
          </select>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="name">Name A-Z</option>
          </select>
        </div>
      </Card>

      {/* Contacts List */}
      <div className="space-y-4">
        {filteredContacts.map((contact) => {
          const StatusIcon = getStatusIcon(contact.status)
          
          return (
            <Card key={contact.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-1 bg-gray-100 rounded-full">
                      <StatusIcon className="h-4 w-4 text-gray-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{contact.name}</h3>
                    <Badge className={getStatusColor(contact.status)}>
                      {contact.status}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {formatDate(contact.createdAt)}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="flex items-center space-x-2 text-sm text-gray-600 mb-1">
                        <Mail className="h-4 w-4" />
                        <span>{contact.email}</span>
                      </div>
                      {contact.phone && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-1">
                          <Phone className="h-4 w-4" />
                          <span>{contact.phone}</span>
                        </div>
                      )}
                      <div className="text-sm text-gray-600">
                        <strong>Subject:</strong> {contact.subject}
                      </div>
                      {contact.propertyInterest && (
                        <div className="text-sm text-gray-600">
                          <strong>Property:</strong> {contact.propertyInterest}
                        </div>
                      )}
                    </div>
                    <div>
                      <div className="text-sm text-gray-600 mb-1">
                        <strong>Preferred Contact:</strong> {contact.preferredContact}
                      </div>
                      {contact.agent && (
                        <div className="text-sm text-gray-600">
                          <strong>Assigned Agent:</strong> {contact.agent}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <p className="text-sm text-gray-700">{contact.message}</p>
                  </div>
                </div>

                <div className="flex flex-col space-y-2 ml-4">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  
                  {contact.status === 'new' && (
                    <Button 
                      size="sm"
                      onClick={() => updateContactStatus(contact.id, 'contacted')}
                    >
                      Mark Contacted
                    </Button>
                  )}
                  
                  {contact.status === 'contacted' && (
                    <Button 
                      size="sm"
                      onClick={() => updateContactStatus(contact.id, 'closed')}
                    >
                      Mark Closed
                    </Button>
                  )}

                  <div className="flex space-x-1">
                    <Button variant="outline" size="sm">
                      <Mail className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Phone className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          )
        })}

        {filteredContacts.length === 0 && (
          <Card className="p-12 text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No contacts found</h3>
            <p className="text-gray-600">
              {searchQuery || statusFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'No customer inquiries yet'
              }
            </p>
          </Card>
        )}
      </div>
    </div>
  )
}
