'use client'

import { Card } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { 
  Building, 
  Users, 
  MessageSquare, 
  DollarSign,
  TrendingUp,
  TrendingDown,
  Eye,
  Plus
} from 'lucide-react'
import Link from 'next/link'

// Mock data - in a real app, this would come from the database
const stats = [
  {
    title: 'Total Properties',
    value: '127',
    change: '+12%',
    trend: 'up' as const,
    icon: Building,
    color: 'blue'
  },
  {
    title: 'Active Listings',
    value: '89',
    change: '+5%',
    trend: 'up' as const,
    icon: Eye,
    color: 'green'
  },
  {
    title: 'Total Agents',
    value: '8',
    change: '+2',
    trend: 'up' as const,
    icon: Users,
    color: 'purple'
  },
  {
    title: 'New Inquiries',
    value: '23',
    change: '+18%',
    trend: 'up' as const,
    icon: MessageSquare,
    color: 'orange'
  }
]

const recentProperties = [
  {
    id: '1',
    title: 'Luxury Estate in West Plano',
    price: 850000,
    status: 'for-sale',
    agent: '<PERSON>',
    daysOnMarket: 12
  },
  {
    id: '2',
    title: 'Modern Townhome in Frisco',
    price: 425000,
    status: 'pending',
    agent: '<PERSON>',
    daysOnMarket: 8
  },
  {
    id: '3',
    title: 'Family Home in McKinney',
    price: 375000,
    status: 'for-sale',
    agent: 'Jennifer Martinez',
    daysOnMarket: 25
  }
]

const recentContacts = [
  {
    id: '1',
    name: 'Robert Wilson',
    email: '<EMAIL>',
    property: 'Luxury Estate in West Plano',
    status: 'new',
    date: '2024-01-15'
  },
  {
    id: '2',
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    property: 'Family Home in McKinney',
    status: 'contacted',
    date: '2024-01-14'
  },
  {
    id: '3',
    name: 'David Brown',
    email: '<EMAIL>',
    property: 'Modern Townhome in Frisco',
    status: 'new',
    date: '2024-01-13'
  }
]

export default function AdminDashboard() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">Welcome back! Here's what's happening with your properties.</p>
        </div>
        <div className="flex space-x-3">
          <Button asChild>
            <Link href="/admin/properties/new">
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                <div className="flex items-center mt-2">
                  {stat.trend === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={`text-sm font-medium ${
                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">vs last month</span>
                </div>
              </div>
              <div className={`p-3 rounded-full bg-${stat.color}-100`}>
                <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
              </div>
            </div>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Properties */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Properties</h3>
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/properties">View All</Link>
            </Button>
          </div>
          <div className="space-y-4">
            {recentProperties.map((property) => (
              <div key={property.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{property.title}</h4>
                  <p className="text-sm text-gray-600">Agent: {property.agent}</p>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-sm font-medium text-green-600">
                      ${property.price.toLocaleString()}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      property.status === 'for-sale' 
                        ? 'bg-blue-100 text-blue-800'
                        : property.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {property.status.replace('-', ' ')}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">{property.daysOnMarket} days</p>
                  <p className="text-xs text-gray-400">on market</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Recent Contacts */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Inquiries</h3>
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/contacts">View All</Link>
            </Button>
          </div>
          <div className="space-y-4">
            {recentContacts.map((contact) => (
              <div key={contact.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{contact.name}</h4>
                  <p className="text-sm text-gray-600">{contact.email}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    Interested in: {contact.property}
                  </p>
                </div>
                <div className="text-right">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    contact.status === 'new'
                      ? 'bg-red-100 text-red-800'
                      : contact.status === 'contacted'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {contact.status}
                  </span>
                  <p className="text-xs text-gray-400 mt-1">{contact.date}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button variant="outline" className="h-20 flex-col" asChild>
            <Link href="/admin/properties/new">
              <Building className="h-6 w-6 mb-2" />
              Add New Property
            </Link>
          </Button>
          <Button variant="outline" className="h-20 flex-col" asChild>
            <Link href="/admin/agents/new">
              <Users className="h-6 w-6 mb-2" />
              Add New Agent
            </Link>
          </Button>
          <Button variant="outline" className="h-20 flex-col" asChild>
            <Link href="/admin/contacts">
              <MessageSquare className="h-6 w-6 mb-2" />
              Review Inquiries
            </Link>
          </Button>
        </div>
      </Card>
    </div>
  )
}
