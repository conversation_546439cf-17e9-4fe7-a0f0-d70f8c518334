'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  MapPin, 
  Search, 
  Star, 
  School, 
  ShoppingBag, 
  Coffee, 
  TreePine,
  Car,
  Home,
  TrendingUp,
  Users,
  DollarSign
} from 'lucide-react'

// Mock neighborhood data
const neighborhoods = [
  {
    id: 'west-plano',
    name: 'West Plano',
    city: 'Plano',
    description: 'Prestigious area known for luxury homes and top-rated schools',
    medianPrice: 525000,
    priceRange: '$400K - $1.2M',
    rating: 9.2,
    population: 45000,
    highlights: ['Top Schools', 'Luxury Homes', 'Shopping Centers'],
    amenities: [
      { icon: School, label: 'Excellent Schools', rating: '9/10' },
      { icon: ShoppingBag, label: 'Legacy West', distance: '2 miles' },
      { icon: TreePine, label: 'Parks & Trails', count: '15+' },
      { icon: Coffee, label: 'Restaurants', count: '50+' }
    ],
    schools: {
      elementary: 'Plano Elementary (9/10)',
      middle: 'Plano Middle School (8/10)',
      high: 'Plano Senior High (9/10)'
    },
    commute: {
      downtown: '25 min',
      airport: '30 min',
      highways: 'US-75, Dallas North Tollway'
    },
    demographics: {
      medianAge: 42,
      familyFriendly: 95,
      walkability: 65
    },
    image: '/neighborhood-plano.jpg',
    coordinates: { lat: 33.0198, lng: -96.6989 }
  },
  {
    id: 'frisco-square',
    name: 'Frisco Square',
    city: 'Frisco',
    description: 'Vibrant downtown area with entertainment, dining, and modern living',
    medianPrice: 485000,
    priceRange: '$350K - $800K',
    rating: 9.0,
    population: 35000,
    highlights: ['Entertainment', 'Dining', 'Sports Venues'],
    amenities: [
      { icon: Coffee, label: 'Restaurants', count: '40+' },
      { icon: ShoppingBag, label: 'Shopping', distance: '0.5 miles' },
      { icon: Star, label: 'Events', count: 'Weekly' },
      { icon: Car, label: 'Transit', rating: 'Excellent' }
    ],
    schools: {
      elementary: 'Frisco Elementary (9/10)',
      middle: 'Frisco Middle School (9/10)',
      high: 'Frisco High School (8/10)'
    },
    commute: {
      downtown: '30 min',
      airport: '35 min',
      highways: 'Dallas North Tollway, SH-121'
    },
    demographics: {
      medianAge: 38,
      familyFriendly: 90,
      walkability: 85
    },
    image: '/neighborhood-frisco.jpg',
    coordinates: { lat: 33.1507, lng: -96.8236 }
  },
  {
    id: 'historic-mckinney',
    name: 'Historic McKinney',
    city: 'McKinney',
    description: 'Charming historic downtown with character homes and local businesses',
    medianPrice: 425000,
    priceRange: '$300K - $650K',
    rating: 8.5,
    population: 28000,
    highlights: ['Historic Charm', 'Local Businesses', 'Community Events'],
    amenities: [
      { icon: Coffee, label: 'Local Cafes', count: '20+' },
      { icon: TreePine, label: 'Historic Square', rating: 'Iconic' },
      { icon: Star, label: 'Festivals', count: 'Monthly' },
      { icon: Home, label: 'Character Homes', rating: 'Unique' }
    ],
    schools: {
      elementary: 'McKinney Elementary (8/10)',
      middle: 'McKinney Middle School (8/10)',
      high: 'McKinney High School (7/10)'
    },
    commute: {
      downtown: '35 min',
      airport: '40 min',
      highways: 'US-75, SH-121'
    },
    demographics: {
      medianAge: 45,
      familyFriendly: 85,
      walkability: 75
    },
    image: '/neighborhood-mckinney.jpg',
    coordinates: { lat: 33.1972, lng: -96.6397 }
  },
  {
    id: 'allen-station',
    name: 'Allen Station',
    city: 'Allen',
    description: 'Family-friendly community with excellent schools and recreation',
    medianPrice: 465000,
    priceRange: '$350K - $700K',
    rating: 8.8,
    population: 32000,
    highlights: ['Family-Friendly', 'Recreation', 'New Development'],
    amenities: [
      { icon: School, label: 'Top Schools', rating: '9/10' },
      { icon: TreePine, label: 'Recreation Center', rating: 'State-of-art' },
      { icon: ShoppingBag, label: 'Watters Creek', distance: '1 mile' },
      { icon: Car, label: 'DART Rail', distance: '0.5 miles' }
    ],
    schools: {
      elementary: 'Allen Elementary (9/10)',
      middle: 'Allen Middle School (9/10)',
      high: 'Allen High School (9/10)'
    },
    commute: {
      downtown: '28 min',
      airport: '32 min',
      highways: 'US-75, SH-121'
    },
    demographics: {
      medianAge: 40,
      familyFriendly: 98,
      walkability: 70
    },
    image: '/neighborhood-allen.jpg',
    coordinates: { lat: 33.1031, lng: -96.6706 }
  }
]

export default function NeighborhoodsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedNeighborhood, setSelectedNeighborhood] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState('rating')

  const filteredNeighborhoods = neighborhoods
    .filter(neighborhood => 
      neighborhood.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      neighborhood.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
      neighborhood.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-asc':
          return a.medianPrice - b.medianPrice
        case 'price-desc':
          return b.medianPrice - a.medianPrice
        case 'name':
          return a.name.localeCompare(b.name)
        case 'rating':
        default:
          return b.rating - a.rating
      }
    })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Explore Collin County Neighborhoods
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover the unique character, amenities, and lifestyle of each community to find your perfect neighborhood
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <Card className="p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search neighborhoods by name, city, or features..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="rating">Highest Rated</option>
              <option value="name">Name A-Z</option>
              <option value="price-asc">Price: Low to High</option>
              <option value="price-desc">Price: High to Low</option>
            </select>
          </div>
        </Card>

        {/* Neighborhoods Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {filteredNeighborhoods.map((neighborhood) => (
            <Card key={neighborhood.id} className="overflow-hidden hover:shadow-xl transition-shadow">
              {/* Neighborhood Header */}
              <div className="h-48 bg-gradient-to-br from-blue-400 to-blue-600 relative">
                <div className="absolute inset-0 bg-black/20" />
                <div className="absolute top-4 right-4">
                  <Badge className="bg-white text-gray-900">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    {neighborhood.rating}
                  </Badge>
                </div>
                <div className="absolute bottom-4 left-4">
                  <h3 className="text-2xl font-bold text-white">{neighborhood.name}</h3>
                  <p className="text-blue-100">{neighborhood.city}, TX</p>
                </div>
              </div>

              <div className="p-6">
                <p className="text-gray-600 mb-4">{neighborhood.description}</p>

                {/* Key Stats */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">
                      ${(neighborhood.medianPrice / 1000).toFixed(0)}K
                    </div>
                    <div className="text-xs text-gray-500">Median Price</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">
                      {(neighborhood.population / 1000).toFixed(0)}K
                    </div>
                    <div className="text-xs text-gray-500">Population</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">
                      {neighborhood.demographics.familyFriendly}%
                    </div>
                    <div className="text-xs text-gray-500">Family Score</div>
                  </div>
                </div>

                {/* Highlights */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {neighborhood.highlights.map((highlight, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {highlight}
                    </Badge>
                  ))}
                </div>

                {/* Top Amenities */}
                <div className="space-y-2 mb-6">
                  {neighborhood.amenities.slice(0, 3).map((amenity, idx) => (
                    <div key={idx} className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <amenity.icon className="h-4 w-4 text-blue-600" />
                        <span className="text-gray-700">{amenity.label}</span>
                      </div>
                      <span className="text-gray-500">
                        {amenity.rating || amenity.distance || amenity.count}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Schools */}
                <div className="bg-blue-50 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                    <School className="h-4 w-4 mr-2" />
                    School Districts
                  </h4>
                  <div className="text-sm text-blue-700 space-y-1">
                    <div>{neighborhood.schools.elementary}</div>
                    <div>{neighborhood.schools.high}</div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-3">
                  <Button className="flex-1" asChild>
                    <Link href={`/neighborhoods/${neighborhood.id}`}>
                      <MapPin className="h-4 w-4 mr-2" />
                      Explore Area
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href={`/properties?neighborhood=${neighborhood.name}`}>
                      View Homes
                    </Link>
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredNeighborhoods.length === 0 && (
          <Card className="p-12 text-center">
            <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No neighborhoods found</h3>
            <p className="text-gray-600">
              Try adjusting your search terms to find neighborhoods that match your criteria.
            </p>
          </Card>
        )}

        {/* Quick Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="p-6 text-center">
            <Home className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">25+</div>
            <div className="text-sm text-gray-600">Cities & Communities</div>
          </Card>
          <Card className="p-6 text-center">
            <School className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">A+</div>
            <div className="text-sm text-gray-600">Average School Rating</div>
          </Card>
          <Card className="p-6 text-center">
            <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">5.2%</div>
            <div className="text-sm text-gray-600">Annual Growth</div>
          </Card>
          <Card className="p-6 text-center">
            <Users className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">1.2M</div>
            <div className="text-sm text-gray-600">Total Population</div>
          </Card>
        </div>
      </div>
    </div>
  )
}
