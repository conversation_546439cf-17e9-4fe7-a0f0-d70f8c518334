'use client'

import { useState } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { PropertyCard } from '@/components/PropertyCard'
import { 
  MapPin, 
  Star, 
  School, 
  ShoppingBag, 
  Coffee, 
  TreePine,
  Car,
  Home,
  TrendingUp,
  Users,
  DollarSign,
  ArrowLeft,
  ExternalLink,
  Navigation,
  Clock
} from 'lucide-react'

// Mock data - in a real app, this would be fetched based on the ID
const neighborhoodData = {
  'west-plano': {
    id: 'west-plano',
    name: 'West Plano',
    city: 'Plano',
    description: 'West Plano is one of the most prestigious neighborhoods in Collin County, known for its luxury homes, top-rated schools, and upscale shopping destinations. This master-planned community offers a perfect blend of suburban tranquility and urban convenience.',
    medianPrice: 525000,
    priceRange: '$400K - $1.2M',
    rating: 9.2,
    population: 45000,
    highlights: ['Top Schools', 'Luxury Homes', 'Shopping Centers'],
    amenities: [
      { icon: School, label: 'Excellent Schools', rating: '9/10', description: 'Plano ISD with nationally recognized programs' },
      { icon: ShoppingBag, label: 'Legacy West', distance: '2 miles', description: 'Premier shopping and dining destination' },
      { icon: TreePine, label: 'Parks & Trails', count: '15+', description: 'Extensive park system and walking trails' },
      { icon: Coffee, label: 'Restaurants', count: '50+', description: 'Diverse dining options from casual to fine dining' }
    ],
    schools: {
      elementary: 'Plano Elementary (9/10)',
      middle: 'Plano Middle School (8/10)',
      high: 'Plano Senior High (9/10)',
      private: ['Plano Preparatory School', 'Legacy Christian Academy']
    },
    commute: {
      downtown: '25 min',
      airport: '30 min',
      highways: 'US-75, Dallas North Tollway',
      publicTransit: 'DART Rail access via nearby stations'
    },
    demographics: {
      medianAge: 42,
      familyFriendly: 95,
      walkability: 65,
      medianIncome: 95000,
      homeOwnership: 85
    },
    marketTrends: {
      priceGrowth: '+5.2%',
      daysOnMarket: 28,
      inventory: 'Low',
      appreciation: '+4.8% annually'
    },
    coordinates: { lat: 33.0198, lng: -96.6989 }
  }
}

// Mock properties in the neighborhood
const neighborhoodProperties = [
  {
    id: '1',
    title: 'Luxury Estate in West Plano',
    price: 850000,
    address: '123 Oak Tree Lane, Plano, TX 75024',
    bedrooms: 5,
    bathrooms: 4.5,
    sqft: 4200,
    images: ['/property-1.jpg'],
    type: 'house' as const,
    status: 'for-sale' as const,
    features: ['Pool', 'Garage', 'Fireplace', 'Updated Kitchen'],
    description: 'Stunning luxury home in prestigious West Plano neighborhood.',
    yearBuilt: 2018,
    lotSize: 0.75,
    garage: 3,
    neighborhood: 'West Plano'
  },
  {
    id: '2',
    title: 'Modern Family Home',
    price: 675000,
    address: '456 Maple Drive, Plano, TX 75024',
    bedrooms: 4,
    bathrooms: 3.5,
    sqft: 3200,
    images: ['/property-2.jpg'],
    type: 'house' as const,
    status: 'for-sale' as const,
    features: ['Updated Kitchen', 'Hardwood Floors', 'Large Yard'],
    description: 'Beautiful family home with modern updates.',
    yearBuilt: 2015,
    lotSize: 0.5,
    garage: 2,
    neighborhood: 'West Plano'
  }
]

export default function NeighborhoodDetailPage() {
  const params = useParams()
  const [activeTab, setActiveTab] = useState('overview')

  const neighborhood = neighborhoodData[params.id as keyof typeof neighborhoodData]

  if (!neighborhood) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Neighborhood Not Found</h1>
          <Link href="/neighborhoods">
            <Button>Back to Neighborhoods</Button>
          </Link>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'amenities', label: 'Amenities' },
    { id: 'schools', label: 'Schools' },
    { id: 'market', label: 'Market Data' },
    { id: 'properties', label: 'Properties' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/neighborhoods">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Neighborhoods
                </Link>
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{neighborhood.name}</h1>
                <p className="text-gray-600">{neighborhood.city}, Texas</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-white text-gray-900 border">
                <Star className="h-3 w-3 mr-1 fill-current" />
                {neighborhood.rating}
              </Badge>
              <Button>
                <Home className="h-4 w-4 mr-2" />
                View Properties
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="h-64 bg-gradient-to-br from-blue-400 to-blue-600 relative">
        <div className="absolute inset-0 bg-black/20" />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <h2 className="text-4xl font-bold mb-2">{neighborhood.name}</h2>
            <p className="text-xl text-blue-100">{neighborhood.description.split('.')[0]}</p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 text-center">
            <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              ${(neighborhood.medianPrice / 1000).toFixed(0)}K
            </div>
            <div className="text-sm text-gray-600">Median Home Price</div>
          </Card>
          <Card className="p-6 text-center">
            <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {(neighborhood.population / 1000).toFixed(0)}K
            </div>
            <div className="text-sm text-gray-600">Population</div>
          </Card>
          <Card className="p-6 text-center">
            <School className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">9/10</div>
            <div className="text-sm text-gray-600">School Rating</div>
          </Card>
          <Card className="p-6 text-center">
            <TrendingUp className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {neighborhood.marketTrends.priceGrowth}
            </div>
            <div className="text-sm text-gray-600">Price Growth</div>
          </Card>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card className="p-6 mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">About {neighborhood.name}</h3>
                <p className="text-gray-700 leading-relaxed">{neighborhood.description}</p>
              </Card>

              <Card className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Transportation & Commute</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Commute Times</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Downtown Dallas:</span>
                        <span className="text-gray-900">{neighborhood.commute.downtown}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">DFW Airport:</span>
                        <span className="text-gray-900">{neighborhood.commute.airport}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Transportation</h4>
                    <div className="space-y-2 text-sm">
                      <div className="text-gray-700">{neighborhood.commute.highways}</div>
                      <div className="text-gray-700">{neighborhood.commute.publicTransit}</div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            <div>
              <Card className="p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Demographics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Median Age:</span>
                    <span className="text-gray-900">{neighborhood.demographics.medianAge}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Family Score:</span>
                    <span className="text-gray-900">{neighborhood.demographics.familyFriendly}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Walkability:</span>
                    <span className="text-gray-900">{neighborhood.demographics.walkability}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Home Ownership:</span>
                    <span className="text-gray-900">{neighborhood.demographics.homeOwnership}%</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Interactive Map</h3>
                <div className="h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">Interactive map would be displayed here</p>
                    <Button variant="outline" size="sm" className="mt-2">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open in Maps
                    </Button>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'amenities' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {neighborhood.amenities.map((amenity, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <amenity.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">{amenity.label}</h3>
                    <p className="text-sm text-gray-600 mb-2">{amenity.description}</p>
                    <div className="text-sm text-blue-600">
                      {amenity.rating || amenity.distance || amenity.count}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'schools' && (
          <div className="space-y-6">
            <Card className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Public Schools</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900">Elementary</h4>
                  <p className="text-sm text-blue-700">{neighborhood.schools.elementary}</p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900">Middle School</h4>
                  <p className="text-sm text-blue-700">{neighborhood.schools.middle}</p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900">High School</h4>
                  <p className="text-sm text-blue-700">{neighborhood.schools.high}</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Private Schools</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {neighborhood.schools.private.map((school, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg">
                    <h4 className="font-medium text-gray-900">{school}</h4>
                    <p className="text-sm text-gray-600">Private Institution</p>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'market' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Market Trends</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Price Growth (YoY):</span>
                  <span className="text-green-600 font-semibold">{neighborhood.marketTrends.priceGrowth}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Days on Market:</span>
                  <span className="text-gray-900">{neighborhood.marketTrends.daysOnMarket} days</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Inventory Level:</span>
                  <span className="text-gray-900">{neighborhood.marketTrends.inventory}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Annual Appreciation:</span>
                  <span className="text-green-600 font-semibold">{neighborhood.marketTrends.appreciation}</span>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Price Ranges</h3>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-gray-600">Median Price</span>
                    <span className="text-sm font-medium">${neighborhood.medianPrice.toLocaleString()}</span>
                  </div>
                  <div className="text-lg font-bold text-green-600">{neighborhood.priceRange}</div>
                </div>
                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-600">
                    Prices vary based on home size, age, and specific location within the neighborhood.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'properties' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Properties in {neighborhood.name}
              </h3>
              <Button asChild>
                <Link href={`/properties?neighborhood=${neighborhood.name}`}>
                  View All Properties
                </Link>
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {neighborhoodProperties.map((property) => (
                <PropertyCard key={property.id} property={property} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
