'use client'

import { useState, useEffect } from 'react'
import { PropertyCard } from '@/components/PropertyCard'
import { PropertyFilters } from '@/components/properties/PropertyFilters'
import { PropertySort } from '@/components/properties/PropertySort'
import { PropertyPagination } from '@/components/properties/PropertyPagination'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Filter, Grid, List, MapPin } from 'lucide-react'
import { Property, SearchFilters } from '@/types'

// Mock data - in a real app, this would come from an API
const mockProperties: Property[] = [
  {
    id: '1',
    title: 'Luxury Estate in Plano',
    price: 850000,
    address: '123 Oak Tree Lane, Plano, TX 75024',
    bedrooms: 5,
    bathrooms: 4.5,
    sqft: 4200,
    images: ['/property-1.jpg'],
    type: 'house',
    status: 'for-sale',
    features: ['Pool', 'Garage', 'Fireplace', 'Updated Kitchen'],
    description: 'Stunning luxury home in prestigious Plano neighborhood with top-rated schools.',
    yearBuilt: 2018,
    lotSize: 0.75,
    garage: 3,
    neighborhood: 'West Plano',
    schools: {
      elementary: 'Plano Elementary (9/10)',
      middle: 'Plano Middle School (8/10)',
      high: 'Plano Senior High (9/10)'
    }
  },
  {
    id: '2',
    title: 'Modern Townhome in Frisco',
    price: 425000,
    address: '456 Maple Street, Frisco, TX 75034',
    bedrooms: 3,
    bathrooms: 2.5,
    sqft: 2100,
    images: ['/property-2.jpg'],
    type: 'townhouse',
    status: 'for-sale',
    features: ['Patio', 'Garage', 'Open Floor Plan'],
    description: 'Beautiful modern townhome in the heart of Frisco with easy access to shopping and dining.',
    yearBuilt: 2020,
    lotSize: 0.15,
    garage: 2,
    neighborhood: 'Frisco Square',
    schools: {
      elementary: 'Frisco Elementary (9/10)',
      middle: 'Frisco Middle School (9/10)',
      high: 'Frisco High School (8/10)'
    }
  },
  // Add more properties...
]

export default function PropertiesPage() {
  const [properties, setProperties] = useState<Property[]>(mockProperties)
  const [filteredProperties, setFilteredProperties] = useState<Property[]>(mockProperties)
  const [filters, setFilters] = useState<SearchFilters>({})
  const [sortBy, setSortBy] = useState<string>('newest')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(12)

  // Filter and sort properties
  useEffect(() => {
    let filtered = [...properties]

    // Apply filters
    if (filters.minPrice) {
      filtered = filtered.filter(p => p.price >= filters.minPrice!)
    }
    if (filters.maxPrice) {
      filtered = filtered.filter(p => p.price <= filters.maxPrice!)
    }
    if (filters.bedrooms) {
      filtered = filtered.filter(p => p.bedrooms >= filters.bedrooms!)
    }
    if (filters.bathrooms) {
      filtered = filtered.filter(p => p.bathrooms >= filters.bathrooms!)
    }
    if (filters.propertyType && filters.propertyType !== 'any') {
      filtered = filtered.filter(p => p.type === filters.propertyType || p.propertyType === filters.propertyType)
    }
    if (filters.city) {
      filtered = filtered.filter(p => 
        p.address.toLowerCase().includes(filters.city!.toLowerCase()) ||
        p.neighborhood?.toLowerCase().includes(filters.city!.toLowerCase())
      )
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-asc':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-desc':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'size-asc':
        filtered.sort((a, b) => (a.sqft || 0) - (b.sqft || 0))
        break
      case 'size-desc':
        filtered.sort((a, b) => (b.sqft || 0) - (a.sqft || 0))
        break
      case 'newest':
        filtered.sort((a, b) => (b.yearBuilt || 0) - (a.yearBuilt || 0))
        break
      case 'oldest':
        filtered.sort((a, b) => (a.yearBuilt || 0) - (b.yearBuilt || 0))
        break
    }

    setFilteredProperties(filtered)
    setCurrentPage(1)
  }, [properties, filters, sortBy])

  // Pagination
  const totalPages = Math.ceil(filteredProperties.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentProperties = filteredProperties.slice(startIndex, endIndex)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Properties for Sale</h1>
              <p className="text-gray-600 mt-2">
                {filteredProperties.length} properties found in Collin County
              </p>
            </div>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="md:hidden"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <PropertyFilters filters={filters} onFiltersChange={setFilters} />
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Sort and View Controls */}
            <div className="flex items-center justify-between mb-6">
              <PropertySort sortBy={sortBy} onSortChange={setSortBy} />
              <Button variant="outline" size="sm">
                <MapPin className="h-4 w-4 mr-2" />
                Map View
              </Button>
            </div>

            {/* Properties Grid/List */}
            {currentProperties.length > 0 ? (
              <>
                <div className={`grid gap-6 mb-8 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
                    : 'grid-cols-1'
                }`}>
                  {currentProperties.map((property) => (
                    <PropertyCard 
                      key={property.id} 
                      property={property}
                      onFavorite={(id) => console.log('Favorite:', id)}
                    />
                  ))}
                </div>

                {/* Pagination */}
                <PropertyPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                />
              </>
            ) : (
              <Card className="p-12 text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No properties found
                </h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search filters to see more results.
                </p>
                <Button onClick={() => setFilters({})}>
                  Clear Filters
                </Button>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
