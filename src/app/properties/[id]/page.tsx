'use client'

import { useState } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ContactForm } from '@/components/properties/ContactForm'
import { PropertyGallery } from '@/components/properties/PropertyGallery'
import { PropertyFeatures } from '@/components/properties/PropertyFeatures'
import { PropertyMap } from '@/components/properties/PropertyMap'
import { SimilarProperties } from '@/components/properties/SimilarProperties'
import { 
  Bed, 
  Bath, 
  Square, 
  MapPin, 
  Calendar, 
  Car, 
  Heart, 
  Share2,
  Phone,
  Mail,
  ArrowLeft,
  School,
  DollarSign
} from 'lucide-react'
import { Property } from '@/types'
import { formatPrice, formatNumber } from '@/lib/utils'

// Mock property data - in a real app, this would be fetched based on the ID
const mockProperty: Property = {
  id: '1',
  title: 'Luxury Estate in Plano',
  price: 850000,
  address: '123 Oak Tree Lane, Plano, TX 75024',
  bedrooms: 5,
  bathrooms: 4.5,
  sqft: 4200,
  images: [
    '/property-1.jpg',
    '/property-1-2.jpg',
    '/property-1-3.jpg',
    '/property-1-4.jpg',
    '/property-1-5.jpg'
  ],
  type: 'house',
  status: 'for-sale',
  features: ['Pool', 'Garage', 'Fireplace', 'Updated Kitchen', 'Hardwood Floors', 'Granite Counters'],
  description: 'Stunning luxury home in prestigious Plano neighborhood with top-rated schools. This beautiful estate features an open floor plan, gourmet kitchen with granite countertops, spacious master suite, and resort-style backyard with pool. Located in highly sought-after West Plano with easy access to Legacy West shopping and dining.',
  yearBuilt: 2018,
  lotSize: 0.75,
  garage: 3,
  neighborhood: 'West Plano',
  schools: {
    elementary: 'Plano Elementary (9/10)',
    middle: 'Plano Middle School (8/10)',
    high: 'Plano Senior High (9/10)'
  },
  daysOnMarket: 12,
  mlsNumber: 'MLS123456'
}

export default function PropertyDetailPage() {
  const params = useParams()
  const [showContactForm, setShowContactForm] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)

  // In a real app, you would fetch the property data based on params.id
  const property = mockProperty

  if (!property) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Property Not Found</h1>
          <Link href="/properties">
            <Button>Back to Properties</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/properties">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Properties
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFavorited(!isFavorited)}
              >
                <Heart className={`h-4 w-4 mr-2 ${isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                Save
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Property Gallery */}
            <PropertyGallery images={property.images} title={property.title} />

            {/* Property Info */}
            <Card className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{property.title}</h1>
                  <div className="flex items-center text-gray-600 mb-4">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{property.address}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-green-600 mb-1">
                    {formatPrice(property.price)}
                  </div>
                  {property.mlsNumber && (
                    <div className="text-sm text-gray-500">MLS# {property.mlsNumber}</div>
                  )}
                </div>
              </div>

              {/* Property Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <Bed className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                  <div className="font-semibold">{property.bedrooms}</div>
                  <div className="text-sm text-gray-600">Bedrooms</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <Bath className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                  <div className="font-semibold">{property.bathrooms}</div>
                  <div className="text-sm text-gray-600">Bathrooms</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <Square className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                  <div className="font-semibold">{formatNumber(property.sqft || 0)}</div>
                  <div className="text-sm text-gray-600">Sq Ft</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <Car className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                  <div className="font-semibold">{property.garage}</div>
                  <div className="text-sm text-gray-600">Garage</div>
                </div>
              </div>

              {/* Status and Details */}
              <div className="flex flex-wrap gap-4 mb-6">
                <Badge variant="default" className="capitalize">
                  {property.status.replace('-', ' ')}
                </Badge>
                <Badge variant="outline" className="capitalize">
                  {property.type}
                </Badge>
                {property.yearBuilt && (
                  <Badge variant="outline">
                    <Calendar className="h-3 w-3 mr-1" />
                    Built {property.yearBuilt}
                  </Badge>
                )}
                {property.daysOnMarket && (
                  <Badge variant="outline">
                    {property.daysOnMarket} days on market
                  </Badge>
                )}
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Description</h3>
                <p className="text-gray-700 leading-relaxed">{property.description}</p>
              </div>
            </Card>

            {/* Features */}
            <PropertyFeatures features={property.features} />

            {/* Schools */}
            {property.schools && (
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <School className="h-5 w-5 mr-2" />
                  School Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900">Elementary</div>
                    <div className="text-sm text-blue-700">{property.schools.elementary}</div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900">Middle School</div>
                    <div className="text-sm text-blue-700">{property.schools.middle}</div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900">High School</div>
                    <div className="text-sm text-blue-700">{property.schools.high}</div>
                  </div>
                </div>
              </Card>
            )}

            {/* Map */}
            <PropertyMap address={property.address} />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Card */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Contact Agent</h3>
              <div className="space-y-4">
                <Button 
                  className="w-full" 
                  onClick={() => setShowContactForm(true)}
                >
                  <Mail className="h-4 w-4 mr-2" />
                  Request Information
                </Button>
                <Button variant="outline" className="w-full">
                  <Phone className="h-4 w-4 mr-2" />
                  Call (*************
                </Button>
                <Button variant="outline" className="w-full">
                  Schedule Tour
                </Button>
              </div>
            </Card>

            {/* Property Details */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Property Details</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Property Type:</span>
                  <span className="capitalize">{property.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Year Built:</span>
                  <span>{property.yearBuilt}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Lot Size:</span>
                  <span>{property.lotSize} acres</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Neighborhood:</span>
                  <span>{property.neighborhood}</span>
                </div>
                {property.mlsNumber && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">MLS Number:</span>
                    <span>{property.mlsNumber}</span>
                  </div>
                )}
              </div>
            </Card>

            {/* Mortgage Calculator */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <DollarSign className="h-5 w-5 mr-2" />
                Mortgage Calculator
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm text-gray-600">Home Price</label>
                  <div className="text-lg font-semibold">{formatPrice(property.price)}</div>
                </div>
                <div>
                  <label className="text-sm text-gray-600">Down Payment (20%)</label>
                  <div className="text-lg font-semibold">{formatPrice(property.price * 0.2)}</div>
                </div>
                <div>
                  <label className="text-sm text-gray-600">Est. Monthly Payment</label>
                  <div className="text-lg font-semibold text-green-600">
                    {formatPrice((property.price * 0.8 * 0.006))} /mo
                  </div>
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  Get Pre-Approved
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* Similar Properties */}
        <div className="mt-12">
          <SimilarProperties currentPropertyId={property.id} />
        </div>
      </div>

      {/* Contact Form Modal */}
      {showContactForm && (
        <ContactForm
          propertyId={property.id}
          propertyTitle={property.title}
          onClose={() => setShowContactForm(false)}
        />
      )}
    </div>
  )
}
