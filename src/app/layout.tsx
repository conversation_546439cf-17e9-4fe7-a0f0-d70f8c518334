import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { AuthProvider } from "@/contexts/AuthContext";
import { Analytics } from "@/components/Analytics";
import { PerformanceMonitor } from "@/components/PerformanceMonitor";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Collin County Homes - Premier Real Estate in Texas",
  description: "Find your dream home in Collin County, Texas. Browse luxury homes, condos, and properties in Plano, Frisco, McKinney, Allen, and more.",
  keywords: "Collin County homes, Texas real estate, Plano homes, Frisco homes, McKinney homes, Allen homes",
  authors: [{ name: "Collin County Homes" }],
  openGraph: {
    title: "Collin County Homes - Premier Real Estate in Texas",
    description: "Find your dream home in Collin County, Texas",
    url: "https://collincounty.homes",
    siteName: "Collin County Homes",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
          <Analytics />
          <PerformanceMonitor />
        </AuthProvider>
      </body>
    </html>
  );
}
