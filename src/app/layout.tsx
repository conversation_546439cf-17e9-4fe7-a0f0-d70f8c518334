import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Collin County Homes - Premier Real Estate in Texas",
  description: "Find your dream home in Collin County, Texas. Browse luxury homes, condos, and properties in Plano, Frisco, McKinney, Allen, and more.",
  keywords: "Collin County homes, Texas real estate, Plano homes, Frisco homes, McKinney homes, Allen homes",
  authors: [{ name: "Collin County Homes" }],
  openGraph: {
    title: "Collin County Homes - Premier Real Estate in Texas",
    description: "Find your dream home in Collin County, Texas",
    url: "https://collincounty.homes",
    siteName: "Collin County Homes",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased`}>
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
