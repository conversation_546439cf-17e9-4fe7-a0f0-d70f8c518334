export interface Property {
  id: string
  title: string
  description: string
  price: number
  address: string
  city?: string
  state?: string
  zipCode?: string
  bedrooms: number
  bathrooms: number
  sqft?: number
  squareFootage?: number
  lotSize?: number
  yearBuilt?: number
  garage?: number
  type?: 'house' | 'townhouse' | 'condo' | 'land'
  propertyType?: 'single-family' | 'townhouse' | 'condo' | 'land' | 'commercial'
  status: 'for-sale' | 'sold' | 'pending' | 'off-market'
  images: string[]
  features: string[]
  neighborhood?: string
  schoolDistrict?: string
  schools?: {
    elementary?: string
    middle?: string
    high?: string
  }
  hoaFees?: number
  taxAmount?: number
  daysOnMarket?: number
  mlsNumber?: string
  agentId?: string
  latitude?: number
  longitude?: number
  createdAt?: string
  updatedAt?: string
}

export interface Agent {
  id: string
  name: string
  email: string
  phone: string
  bio?: string
  image?: string
  licenseNumber?: string
  yearsExperience?: number
  specialties: string[]
  socialMedia?: {
    facebook?: string
    instagram?: string
    linkedin?: string
    twitter?: string
  }
  createdAt: string
  updatedAt: string
}

export interface SearchFilters {
  minPrice?: number
  maxPrice?: number
  bedrooms?: number
  bathrooms?: number
  propertyType?: Property['propertyType']
  city?: string
  neighborhood?: string
  minSquareFootage?: number
  maxSquareFootage?: number
  features?: string[]
  sortBy?: 'price-asc' | 'price-desc' | 'newest' | 'oldest' | 'size-asc' | 'size-desc'
}

export interface ContactForm {
  name: string
  email: string
  phone?: string
  message: string
  propertyId?: string
  preferredContact?: 'email' | 'phone'
}

export interface User {
  id: string
  email: string
  name: string
  role: 'buyer' | 'agent' | 'admin'
  phone?: string
  preferences?: {
    savedSearches: SearchFilters[]
    favoriteProperties: string[]
    notifications: boolean
  }
  createdAt: string
  updatedAt: string
}
