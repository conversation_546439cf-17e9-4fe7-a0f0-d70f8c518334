'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User, AuthState, authService, supabase } from '@/lib/auth'

interface AuthContextType extends AuthState {
  signUp: (email: string, password: string, userData: { name: string; role: 'buyer' | 'agent'; phone?: string }) => Promise<{ error: string | null }>
  signIn: (email: string, password: string) => Promise<{ error: string | null }>
  signOut: () => Promise<{ error: string | null }>
  updateProfile: (updates: Partial<User>) => Promise<{ error: string | null }>
  resetPassword: (email: string) => Promise<{ error: string | null }>
  updatePassword: (newPassword: string) => Promise<{ error: string | null }>
  addToFavorites: (propertyId: string) => Promise<{ error: string | null }>
  removeFromFavorites: (propertyId: string) => Promise<{ error: string | null }>
  getFavorites: () => Promise<{ data: any[]; error: string | null }>
  saveSearch: (name: string, filters: any) => Promise<{ data: any; error: string | null }>
  getSavedSearches: () => Promise<{ data: any[]; error: string | null }>
  deleteSavedSearch: (searchId: string) => Promise<{ error: string | null }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  })

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const user = await authService.getCurrentUser()
        setState(prev => ({ ...prev, user, loading: false }))
      } catch (error) {
        setState(prev => ({ ...prev, loading: false, error: 'Failed to load user session' }))
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          const user = await authService.getCurrentUser()
          setState(prev => ({ ...prev, user, loading: false, error: null }))
        } else if (event === 'SIGNED_OUT') {
          setState(prev => ({ ...prev, user: null, loading: false, error: null }))
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signUp = async (email: string, password: string, userData: { name: string; role: 'buyer' | 'agent'; phone?: string }) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { error } = await authService.signUp(email, password, userData)
    
    if (error) {
      setState(prev => ({ ...prev, loading: false, error }))
      return { error }
    }

    setState(prev => ({ ...prev, loading: false }))
    return { error: null }
  }

  const signIn = async (email: string, password: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { error } = await authService.signIn(email, password)
    
    if (error) {
      setState(prev => ({ ...prev, loading: false, error }))
      return { error }
    }

    // User will be set by the auth state change listener
    return { error: null }
  }

  const signOut = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { error } = await authService.signOut()
    
    if (error) {
      setState(prev => ({ ...prev, loading: false, error }))
      return { error }
    }

    // User will be cleared by the auth state change listener
    return { error: null }
  }

  const updateProfile = async (updates: Partial<User>) => {
    if (!state.user) return { error: 'Not authenticated' }

    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { data, error } = await authService.updateProfile(state.user.id, updates)
    
    if (error) {
      setState(prev => ({ ...prev, loading: false, error }))
      return { error }
    }

    setState(prev => ({ 
      ...prev, 
      user: data ? { ...prev.user!, ...data } : prev.user,
      loading: false 
    }))
    
    return { error: null }
  }

  const resetPassword = async (email: string) => {
    return await authService.resetPassword(email)
  }

  const updatePassword = async (newPassword: string) => {
    return await authService.updatePassword(newPassword)
  }

  const addToFavorites = async (propertyId: string) => {
    return await authService.addToFavorites(propertyId)
  }

  const removeFromFavorites = async (propertyId: string) => {
    return await authService.removeFromFavorites(propertyId)
  }

  const getFavorites = async () => {
    return await authService.getFavorites()
  }

  const saveSearch = async (name: string, filters: any) => {
    return await authService.saveSearch(name, filters)
  }

  const getSavedSearches = async () => {
    return await authService.getSavedSearches()
  }

  const deleteSavedSearch = async (searchId: string) => {
    return await authService.deleteSavedSearch(searchId)
  }

  const value: AuthContextType = {
    ...state,
    signUp,
    signIn,
    signOut,
    updateProfile,
    resetPassword,
    updatePassword,
    addToFavorites,
    removeFromFavorites,
    getFavorites,
    saveSearch,
    getSavedSearches,
    deleteSavedSearch
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
