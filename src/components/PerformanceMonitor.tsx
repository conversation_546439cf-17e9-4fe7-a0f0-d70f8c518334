'use client'

import { useEffect } from 'react'

interface PerformanceMetrics {
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
}

export function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and if performance API is available
    if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined') {
      return
    }

    const metrics: PerformanceMetrics = {}

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0] as PerformanceEntry
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime
      }

      // Time to First Byte
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1] as any
            if (lastEntry) {
              metrics.lcp = lastEntry.startTime
            }
          })
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (entry.name === 'first-input') {
                metrics.fid = entry.processingStart - entry.startTime
              }
            })
          })
          fidObserver.observe({ entryTypes: ['first-input'] })

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value
              }
            })
            metrics.cls = clsValue
          })
          clsObserver.observe({ entryTypes: ['layout-shift'] })

          // Send metrics after page load
          setTimeout(() => {
            sendMetrics(metrics)
          }, 5000)
        } catch (error) {
          console.warn('Performance monitoring not supported:', error)
        }
      }
    }

    // Send metrics to analytics service
    const sendMetrics = (metrics: PerformanceMetrics) => {
      // In a real app, you would send these to your analytics service
      console.log('Performance Metrics:', metrics)
      
      // Example: Send to Google Analytics 4
      if (typeof gtag !== 'undefined') {
        Object.entries(metrics).forEach(([metric, value]) => {
          if (value !== undefined) {
            gtag('event', 'web_vital', {
              name: metric,
              value: Math.round(value),
              event_category: 'Web Vitals',
            })
          }
        })
      }

      // Example: Send to custom analytics endpoint
      // fetch('/api/analytics/performance', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(metrics),
      // }).catch(console.error)
    }

    // Start measuring when page is loaded
    if (document.readyState === 'complete') {
      measureWebVitals()
    } else {
      window.addEventListener('load', measureWebVitals)
    }

    // Cleanup
    return () => {
      window.removeEventListener('load', measureWebVitals)
    }
  }, [])

  // This component doesn't render anything
  return null
}

// Hook for measuring custom performance metrics
export function usePerformanceMetric(name: string) {
  useEffect(() => {
    const startTime = performance.now()

    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Mark the performance metric
      performance.mark(`${name}-start`, { startTime })
      performance.mark(`${name}-end`, { startTime: endTime })
      performance.measure(name, `${name}-start`, `${name}-end`)

      console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)
    }
  }, [name])
}

// Component for lazy loading images with performance optimization
export function OptimizedImage({ 
  src, 
  alt, 
  className, 
  priority = false,
  ...props 
}: {
  src: string
  alt: string
  className?: string
  priority?: boolean
  [key: string]: any
}) {
  return (
    <img
      src={src}
      alt={alt}
      className={className}
      loading={priority ? 'eager' : 'lazy'}
      decoding="async"
      {...props}
    />
  )
}

// Utility function to preload critical resources
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return

  // Preload critical fonts
  const fontPreloads = [
    '/fonts/inter-var.woff2',
    // Add your critical fonts here
  ]

  fontPreloads.forEach(font => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = font
    link.as = 'font'
    link.type = 'font/woff2'
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })

  // Preload critical images
  const imagePreloads = [
    '/hero-image.jpg',
    '/logo.png',
    // Add your critical images here
  ]

  imagePreloads.forEach(image => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = image
    link.as = 'image'
    document.head.appendChild(link)
  })
}

// Service Worker registration for caching
export function registerServiceWorker() {
  if (
    typeof window !== 'undefined' &&
    'serviceWorker' in navigator &&
    process.env.NODE_ENV === 'production'
  ) {
    window.addEventListener('load', () => {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration)
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError)
        })
    })
  }
}
