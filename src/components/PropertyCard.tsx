import Image from 'next/image'
import Link from 'next/link'
import { Property } from '@/types'
import { formatPrice, formatNumber } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Bed, Bath, Square, MapPin, Heart } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface PropertyCardProps {
  property: Property
  onFavorite?: (propertyId: string) => void
  isFavorited?: boolean
}

export function PropertyCard({ property, onFavorite, isFavorited = false }: PropertyCardProps) {
  const primaryImage = property.images[0] || '/placeholder-property.jpg'

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <div className="relative">
        <Link href={`/properties/${property.id}`}>
          <div className="relative h-48 w-full">
            <Image
              src={primaryImage}
              alt={property.title}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
        </Link>

        {/* Status Badge */}
        <div className="absolute top-3 left-3">
          <Badge
            variant={property.status === 'for-sale' ? 'default' : 'secondary'}
            className="capitalize"
          >
            {property.status.replace('-', ' ')}
          </Badge>
        </div>

        {/* Favorite Button */}
        {onFavorite && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-3 right-3 bg-white/80 hover:bg-white"
            onClick={() => onFavorite(property.id)}
          >
            <Heart
              className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : 'text-gray-600'}`}
            />
          </Button>
        )}

        {/* Days on Market */}
        {property.daysOnMarket && (
          <div className="absolute bottom-3 left-3">
            <Badge variant="outline" className="bg-white/90">
              {property.daysOnMarket} days on market
            </Badge>
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Price */}
          <div className="flex items-center justify-between">
            <h3 className="text-2xl font-bold text-green-600">
              {formatPrice(property.price)}
            </h3>
            {property.mlsNumber && (
              <span className="text-sm text-gray-500">
                MLS# {property.mlsNumber}
              </span>
            )}
          </div>

          {/* Property Details */}
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <Bed className="h-4 w-4" />
              <span>{property.bedrooms} bed</span>
            </div>
            <div className="flex items-center space-x-1">
              <Bath className="h-4 w-4" />
              <span>{property.bathrooms} bath</span>
            </div>
            <div className="flex items-center space-x-1">
              <Square className="h-4 w-4" />
              <span>{formatNumber(property.sqft || property.squareFootage || 0)} sqft</span>
            </div>
          </div>

          {/* Address */}
          <div className="flex items-start space-x-1 text-sm text-gray-600">
            <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <span className="line-clamp-2">
              {property.address}
            </span>
          </div>

          {/* Property Type */}
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="capitalize">
              {(property.type || property.propertyType || 'house').replace('-', ' ')}
            </Badge>
            {property.yearBuilt && (
              <span className="text-sm text-gray-500">
                Built {property.yearBuilt}
              </span>
            )}
          </div>

          {/* Description Preview */}
          <p className="text-sm text-gray-600 line-clamp-2">
            {property.description}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
