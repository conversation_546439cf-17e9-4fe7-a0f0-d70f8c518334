import Head from 'next/head'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  price?: number
  currency?: string
  availability?: 'InStock' | 'OutOfStock' | 'PreOrder'
  propertyType?: string
  bedrooms?: number
  bathrooms?: number
  squareFootage?: number
  address?: {
    street: string
    city: string
    state: string
    zipCode: string
    country?: string
  }
}

export function SEO({
  title = 'Collin County Homes - Your Premier Real Estate Destination',
  description = 'Find your dream home in Collin County, Texas. Browse luxury homes, family-friendly neighborhoods, and exceptional schools in Plano, Frisco, McKinney, Allen, and more.',
  keywords = ['real estate', 'homes for sale', 'Collin County', 'Texas', 'Plano', 'Frisco', 'McKinney', 'Allen'],
  image = '/og-image.jpg',
  url,
  type = 'website',
  author = 'Collin County Homes',
  publishedTime,
  modifiedTime,
  section,
  tags,
  price,
  currency = 'USD',
  availability = 'InStock',
  propertyType,
  bedrooms,
  bathrooms,
  squareFootage,
  address
}: SEOProps) {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://collincounty.homes'
  const fullUrl = url ? `${siteUrl}${url}` : siteUrl
  const fullImageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`

  // Generate structured data for real estate listings
  const generateStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': type === 'product' ? 'Product' : 'WebSite',
      name: title,
      description,
      url: fullUrl,
      image: fullImageUrl,
      author: {
        '@type': 'Organization',
        name: author,
        url: siteUrl
      }
    }

    if (type === 'product' && price) {
      return {
        ...baseData,
        '@type': 'Product',
        category: 'Real Estate',
        offers: {
          '@type': 'Offer',
          price: price,
          priceCurrency: currency,
          availability: `https://schema.org/${availability}`,
          seller: {
            '@type': 'Organization',
            name: author
          }
        },
        ...(propertyType && {
          additionalProperty: [
            {
              '@type': 'PropertyValue',
              name: 'Property Type',
              value: propertyType
            },
            ...(bedrooms ? [{
              '@type': 'PropertyValue',
              name: 'Bedrooms',
              value: bedrooms
            }] : []),
            ...(bathrooms ? [{
              '@type': 'PropertyValue',
              name: 'Bathrooms',
              value: bathrooms
            }] : []),
            ...(squareFootage ? [{
              '@type': 'PropertyValue',
              name: 'Square Footage',
              value: squareFootage,
              unitText: 'sqft'
            }] : [])
          ]
        }),
        ...(address && {
          location: {
            '@type': 'Place',
            address: {
              '@type': 'PostalAddress',
              streetAddress: address.street,
              addressLocality: address.city,
              addressRegion: address.state,
              postalCode: address.zipCode,
              addressCountry: address.country || 'US'
            }
          }
        })
      }
    }

    if (type === 'article') {
      return {
        ...baseData,
        '@type': 'Article',
        headline: title,
        ...(publishedTime && { datePublished: publishedTime }),
        ...(modifiedTime && { dateModified: modifiedTime }),
        ...(section && { articleSection: section }),
        ...(tags && { keywords: tags.join(', ') })
      }
    }

    return baseData
  }

  const structuredData = generateStructuredData()

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="Collin County Homes" />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:site" content="@collincountyhomes" />
      <meta name="twitter:creator" content="@collincountyhomes" />

      {/* Additional Meta Tags for Real Estate */}
      {price && (
        <>
          <meta property="product:price:amount" content={price.toString()} />
          <meta property="product:price:currency" content={currency} />
        </>
      )}

      {propertyType && <meta name="property:type" content={propertyType} />}
      {bedrooms && <meta name="property:bedrooms" content={bedrooms.toString()} />}
      {bathrooms && <meta name="property:bathrooms" content={bathrooms.toString()} />}
      {squareFootage && <meta name="property:square_feet" content={squareFootage.toString()} />}

      {/* Geo Meta Tags */}
      {address && (
        <>
          <meta name="geo.placename" content={`${address.city}, ${address.state}`} />
          <meta name="geo.region" content={address.state} />
        </>
      )}

      {/* Article Meta Tags */}
      {type === 'article' && (
        <>
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          {tags && tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />

      {/* Additional SEO Meta Tags */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Mobile and Responsive */}
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Favicon and Icons */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      <meta name="theme-color" content="#2563eb" />
      
      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
    </Head>
  )
}

// Hook for dynamic SEO updates
export function useSEO(seoProps: SEOProps) {
  return <SEO {...seoProps} />
}

// Utility function to generate SEO-friendly URLs
export function generateSEOUrl(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

// Utility function to generate meta description from content
export function generateMetaDescription(content: string, maxLength: number = 160): string {
  const cleanContent = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
  
  if (cleanContent.length <= maxLength) {
    return cleanContent
  }
  
  const truncated = cleanContent.substring(0, maxLength)
  const lastSpace = truncated.lastIndexOf(' ')
  
  return lastSpace > 0 ? truncated.substring(0, lastSpace) + '...' : truncated + '...'
}
