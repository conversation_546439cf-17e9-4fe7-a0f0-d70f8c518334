import Link from 'next/link'
import { Facebook, Instagram, Twitter, Linkedin, Phone, Mail, MapPin } from 'lucide-react'

export function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    'Properties': [
      { name: 'Search Homes', href: '/properties' },
      { name: 'New Listings', href: '/properties?sort=newest' },
      { name: 'Price Reduced', href: '/properties?status=price-reduced' },
      { name: 'Open Houses', href: '/open-houses' },
    ],
    'Neighborhoods': [
      { name: 'Plano', href: '/neighborhoods/plano' },
      { name: 'Frisco', href: '/neighborhoods/frisco' },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/neighborhoods/mckinney' },
      { name: 'Allen', href: '/neighborhoods/allen' },
    ],
    'Resources': [
      { name: 'Market Reports', href: '/market-reports' },
      { name: 'School Information', href: '/schools' },
      { name: 'Mortgage Calculator', href: '/mortgage-calculator' },
      { name: 'Home Valuation', href: '/home-valuation' },
    ],
    'Company': [
      { name: 'About Us', href: '/about' },
      { name: 'Our Agents', href: '/agents' },
      { name: 'Careers', href: '/careers' },
      { name: 'Contact', href: '/contact' },
    ],
  }

  return (
    <footer className="bg-slate-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-4">
              <div className="text-2xl font-bold">
                Collin County
                <span className="text-blue-400">.homes</span>
              </div>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              Your trusted partner in Collin County real estate. We help you find the perfect home 
              in Texas&apos;s most desirable communities.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-blue-400" />
                <span>(*************</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-blue-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-blue-400" />
                <span>123 Main Street, Plano, TX 75023</span>
              </div>
            </div>

            {/* Social Media */}
            <div className="flex space-x-4 mt-6">
              <Link href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Facebook className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Instagram className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Twitter className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Linkedin className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="text-lg font-semibold mb-4">{category}</h3>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link 
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm">
              © {currentYear} Collin County Homes. All rights reserved.
            </div>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
                Terms of Service
              </Link>
              <Link href="/accessibility" className="text-gray-400 hover:text-white text-sm transition-colors">
                Accessibility
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
