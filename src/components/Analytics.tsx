'use client'

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

// Google Analytics 4 Configuration
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID

// Google Tag Manager Configuration
const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID

// Facebook Pixel Configuration
const FB_PIXEL_ID = process.env.NEXT_PUBLIC_FB_PIXEL_ID

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
    fbq: (...args: any[]) => void
  }
}

// Google Analytics 4 Component
export function GoogleAnalytics() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!GA_MEASUREMENT_ID) return

    // Load Google Analytics script
    const script1 = document.createElement('script')
    script1.async = true
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`
    document.head.appendChild(script1)

    const script2 = document.createElement('script')
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${GA_MEASUREMENT_ID}', {
        page_title: document.title,
        page_location: window.location.href,
        send_page_view: true
      });
    `
    document.head.appendChild(script2)

    return () => {
      document.head.removeChild(script1)
      document.head.removeChild(script2)
    }
  }, [])

  useEffect(() => {
    if (!GA_MEASUREMENT_ID || !window.gtag) return

    const url = pathname + searchParams.toString()
    
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: url,
      page_title: document.title,
      page_location: window.location.href,
    })
  }, [pathname, searchParams])

  return null
}

// Google Tag Manager Component
export function GoogleTagManager() {
  useEffect(() => {
    if (!GTM_ID) return

    // Load GTM script
    const script = document.createElement('script')
    script.innerHTML = `
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','${GTM_ID}');
    `
    document.head.appendChild(script)

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  return (
    <noscript>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
        height="0"
        width="0"
        style={{ display: 'none', visibility: 'hidden' }}
      />
    </noscript>
  )
}

// Facebook Pixel Component
export function FacebookPixel() {
  useEffect(() => {
    if (!FB_PIXEL_ID) return

    const script = document.createElement('script')
    script.innerHTML = `
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '${FB_PIXEL_ID}');
      fbq('track', 'PageView');
    `
    document.head.appendChild(script)

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  return (
    <noscript>
      <img
        height="1"
        width="1"
        style={{ display: 'none' }}
        src={`https://www.facebook.com/tr?id=${FB_PIXEL_ID}&ev=PageView&noscript=1`}
        alt=""
      />
    </noscript>
  )
}

// Analytics Event Tracking
export const analytics = {
  // Page view tracking
  pageView: (url: string, title?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'page_view', {
        page_path: url,
        page_title: title || document.title,
      })
    }
  },

  // Property view tracking
  viewProperty: (propertyId: string, propertyTitle: string, price: number) => {
    if (typeof window !== 'undefined') {
      // Google Analytics
      if (window.gtag) {
        window.gtag('event', 'view_item', {
          currency: 'USD',
          value: price,
          items: [{
            item_id: propertyId,
            item_name: propertyTitle,
            category: 'Real Estate',
            price: price,
          }]
        })
      }

      // Facebook Pixel
      if (window.fbq) {
        window.fbq('track', 'ViewContent', {
          content_ids: [propertyId],
          content_type: 'product',
          value: price,
          currency: 'USD'
        })
      }
    }
  },

  // Search tracking
  search: (searchTerm: string, filters?: any) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'search', {
        search_term: searchTerm,
        custom_parameters: filters
      })
    }
  },

  // Contact form submission
  contactSubmit: (propertyId?: string) => {
    if (typeof window !== 'undefined') {
      // Google Analytics
      if (window.gtag) {
        window.gtag('event', 'generate_lead', {
          currency: 'USD',
          value: 1,
          ...(propertyId && { property_id: propertyId })
        })
      }

      // Facebook Pixel
      if (window.fbq) {
        window.fbq('track', 'Lead', {
          content_category: 'Real Estate Inquiry'
        })
      }
    }
  },

  // User registration
  signUp: (method: string) => {
    if (typeof window !== 'undefined') {
      // Google Analytics
      if (window.gtag) {
        window.gtag('event', 'sign_up', {
          method: method
        })
      }

      // Facebook Pixel
      if (window.fbq) {
        window.fbq('track', 'CompleteRegistration')
      }
    }
  },

  // User login
  login: (method: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'login', {
        method: method
      })
    }
  },

  // Property favorite
  addToFavorites: (propertyId: string, propertyTitle: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'add_to_wishlist', {
        currency: 'USD',
        items: [{
          item_id: propertyId,
          item_name: propertyTitle,
          category: 'Real Estate'
        }]
      })
    }
  },

  // Custom event tracking
  customEvent: (eventName: string, parameters?: any) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, parameters)
    }
  },

  // E-commerce tracking for premium features
  purchase: (transactionId: string, value: number, items: any[]) => {
    if (typeof window !== 'undefined') {
      // Google Analytics
      if (window.gtag) {
        window.gtag('event', 'purchase', {
          transaction_id: transactionId,
          value: value,
          currency: 'USD',
          items: items
        })
      }

      // Facebook Pixel
      if (window.fbq) {
        window.fbq('track', 'Purchase', {
          value: value,
          currency: 'USD'
        })
      }
    }
  }
}

// Hook for tracking page views
export function usePageTracking() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '')
    analytics.pageView(url)
  }, [pathname, searchParams])
}

// Combined Analytics Component
export function Analytics() {
  return (
    <>
      <GoogleAnalytics />
      <GoogleTagManager />
      <FacebookPixel />
    </>
  )
}
