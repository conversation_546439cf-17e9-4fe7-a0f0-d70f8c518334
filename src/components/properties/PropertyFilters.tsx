'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { SearchFilters } from '@/types'
import { 
  DollarSign, 
  Bed, 
  Bath, 
  Square, 
  Home, 
  MapPin,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react'

interface PropertyFiltersProps {
  filters: SearchFilters
  onFiltersChange: (filters: SearchFilters) => void
}

export function PropertyFilters({ filters, onFiltersChange }: PropertyFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    price: true,
    bedsBaths: true,
    propertyType: true,
    location: true,
    features: false
  })

  const propertyTypes = [
    { value: 'any', label: 'Any Type' },
    { value: 'house', label: 'House' },
    { value: 'townhouse', label: 'Townhouse' },
    { value: 'condo', label: 'Condo' },
    { value: 'land', label: 'Land' }
  ]

  const popularFeatures = [
    'Pool', 'Garage', 'Fireplace', 'Updated Kitchen', 'Hardwood Floors',
    'Granite Counters', 'Stainless Appliances', 'Master Suite', 'Patio',
    'Basement', 'Home Office', 'Walk-in Closet'
  ]

  const cities = [
    'Plano', 'Frisco', 'McKinney', 'Allen', 'Prosper', 'Richardson',
    'Carrollton', 'Lewisville', 'The Colony', 'Little Elm'
  ]

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value })
  }

  const toggleFeature = (feature: string) => {
    const currentFeatures = filters.features || []
    const newFeatures = currentFeatures.includes(feature)
      ? currentFeatures.filter(f => f !== feature)
      : [...currentFeatures, feature]
    updateFilter('features', newFeatures)
  }

  const clearFilters = () => {
    onFiltersChange({})
  }

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const activeFiltersCount = Object.values(filters).filter(value => 
    value !== undefined && value !== '' && 
    (Array.isArray(value) ? value.length > 0 : true)
  ).length

  return (
    <Card className="p-6 sticky top-4">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        {activeFiltersCount > 0 && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            Clear All ({activeFiltersCount})
          </Button>
        )}
      </div>

      <div className="space-y-6">
        {/* Price Range */}
        <div>
          <button
            onClick={() => toggleSection('price')}
            className="flex items-center justify-between w-full text-left"
          >
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Price Range</span>
            </div>
            {expandedSections.price ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
          
          {expandedSections.price && (
            <div className="mt-3 space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-gray-500 mb-1 block">Min Price</label>
                  <Input
                    type="number"
                    placeholder="$0"
                    value={filters.minPrice || ''}
                    onChange={(e) => updateFilter('minPrice', e.target.value ? Number(e.target.value) : undefined)}
                  />
                </div>
                <div>
                  <label className="text-xs text-gray-500 mb-1 block">Max Price</label>
                  <Input
                    type="number"
                    placeholder="No max"
                    value={filters.maxPrice || ''}
                    onChange={(e) => updateFilter('maxPrice', e.target.value ? Number(e.target.value) : undefined)}
                  />
                </div>
              </div>
              
              {/* Quick Price Buttons */}
              <div className="grid grid-cols-2 gap-2">
                {[
                  { label: 'Under $300K', max: 300000 },
                  { label: '$300K-$500K', min: 300000, max: 500000 },
                  { label: '$500K-$750K', min: 500000, max: 750000 },
                  { label: 'Over $750K', min: 750000 }
                ].map((range, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      updateFilter('minPrice', range.min)
                      updateFilter('maxPrice', range.max)
                    }}
                    className="text-xs"
                  >
                    {range.label}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Beds & Baths */}
        <div>
          <button
            onClick={() => toggleSection('bedsBaths')}
            className="flex items-center justify-between w-full text-left"
          >
            <div className="flex items-center space-x-2">
              <Bed className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Beds & Baths</span>
            </div>
            {expandedSections.bedsBaths ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
          
          {expandedSections.bedsBaths && (
            <div className="mt-3 space-y-3">
              <div>
                <label className="text-xs text-gray-500 mb-2 block">Bedrooms</label>
                <div className="flex flex-wrap gap-2">
                  {[1, 2, 3, 4, 5].map((num) => (
                    <Button
                      key={num}
                      variant={filters.bedrooms === num ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => updateFilter('bedrooms', filters.bedrooms === num ? undefined : num)}
                    >
                      {num}+
                    </Button>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="text-xs text-gray-500 mb-2 block">Bathrooms</label>
                <div className="flex flex-wrap gap-2">
                  {[1, 1.5, 2, 2.5, 3, 4].map((num) => (
                    <Button
                      key={num}
                      variant={filters.bathrooms === num ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => updateFilter('bathrooms', filters.bathrooms === num ? undefined : num)}
                    >
                      {num}+
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Property Type */}
        <div>
          <button
            onClick={() => toggleSection('propertyType')}
            className="flex items-center justify-between w-full text-left"
          >
            <div className="flex items-center space-x-2">
              <Home className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Property Type</span>
            </div>
            {expandedSections.propertyType ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
          
          {expandedSections.propertyType && (
            <div className="mt-3">
              <div className="space-y-2">
                {propertyTypes.map((type) => (
                  <label key={type.value} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="propertyType"
                      value={type.value}
                      checked={filters.propertyType === type.value || (!filters.propertyType && type.value === 'any')}
                      onChange={(e) => updateFilter('propertyType', e.target.value === 'any' ? undefined : e.target.value)}
                      className="text-blue-600"
                    />
                    <span className="text-sm">{type.label}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Location */}
        <div>
          <button
            onClick={() => toggleSection('location')}
            className="flex items-center justify-between w-full text-left"
          >
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Location</span>
            </div>
            {expandedSections.location ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
          
          {expandedSections.location && (
            <div className="mt-3 space-y-3">
              <Input
                placeholder="Search by city or neighborhood"
                value={filters.city || ''}
                onChange={(e) => updateFilter('city', e.target.value || undefined)}
              />
              
              <div className="flex flex-wrap gap-2">
                {cities.map((city) => (
                  <Button
                    key={city}
                    variant={filters.city === city ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => updateFilter('city', filters.city === city ? undefined : city)}
                  >
                    {city}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Features */}
        <div>
          <button
            onClick={() => toggleSection('features')}
            className="flex items-center justify-between w-full text-left"
          >
            <div className="flex items-center space-x-2">
              <Square className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Features</span>
            </div>
            {expandedSections.features ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
          
          {expandedSections.features && (
            <div className="mt-3">
              <div className="flex flex-wrap gap-2">
                {popularFeatures.map((feature) => (
                  <Badge
                    key={feature}
                    variant={filters.features?.includes(feature) ? 'default' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => toggleFeature(feature)}
                  >
                    {feature}
                    {filters.features?.includes(feature) && (
                      <X className="h-3 w-3 ml-1" />
                    )}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  )
}
