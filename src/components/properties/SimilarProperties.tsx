'use client'

import { PropertyCard } from '@/components/PropertyCard'
import { Property } from '@/types'

interface SimilarPropertiesProps {
  currentPropertyId: string
}

// Mock similar properties data
const mockSimilarProperties: Property[] = [
  {
    id: '2',
    title: 'Modern Townhome in Frisco',
    price: 425000,
    address: '456 Maple Street, Frisco, TX 75034',
    bedrooms: 3,
    bathrooms: 2.5,
    sqft: 2100,
    images: ['/property-2.jpg'],
    type: 'townhouse',
    status: 'for-sale',
    features: ['Patio', 'Garage', 'Open Floor Plan'],
    description: 'Beautiful modern townhome in the heart of Frisco with easy access to shopping and dining.',
    yearBuilt: 2020,
    lotSize: 0.15,
    garage: 2,
    neighborhood: 'Frisco Square'
  },
  {
    id: '3',
    title: 'Family Home in McKinney',
    price: 375000,
    address: '789 Pine Avenue, McKinney, TX 75070',
    bedrooms: 4,
    bathrooms: 3,
    sqft: 2800,
    images: ['/property-3.jpg'],
    type: 'house',
    status: 'for-sale',
    features: ['Large Yard', 'Garage', 'Updated Bathrooms'],
    description: 'Perfect family home in established McKinney neighborhood with mature trees.',
    yearBuilt: 2015,
    lotSize: 0.5,
    garage: 2,
    neighborhood: 'Historic McKinney'
  },
  {
    id: '4',
    title: 'New Construction in Allen',
    price: 525000,
    address: '321 Cedar Drive, Allen, TX 75013',
    bedrooms: 4,
    bathrooms: 3.5,
    sqft: 3200,
    images: ['/property-4.jpg'],
    type: 'house',
    status: 'for-sale',
    features: ['New Construction', 'Smart Home', 'Energy Efficient'],
    description: 'Brand new construction with all the latest features and smart home technology.',
    yearBuilt: 2024,
    lotSize: 0.4,
    garage: 2,
    neighborhood: 'Allen Station'
  }
]

export function SimilarProperties({ currentPropertyId }: SimilarPropertiesProps) {
  // Filter out the current property and get similar ones
  const similarProperties = mockSimilarProperties.filter(
    property => property.id !== currentPropertyId
  ).slice(0, 3)

  if (similarProperties.length === 0) {
    return null
  }

  return (
    <div>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Similar Properties</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {similarProperties.map((property) => (
          <PropertyCard 
            key={property.id} 
            property={property}
            onFavorite={(id) => console.log('Favorite:', id)}
          />
        ))}
      </div>
    </div>
  )
}
