'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Home, 
  Car, 
  Waves, 
  Flame, 
  ChefHat, 
  TreePine, 
  Wifi, 
  Shield,
  Lightbulb,
  Wind,
  Thermometer,
  Camera
} from 'lucide-react'

interface PropertyFeaturesProps {
  features: string[]
}

// Map feature names to icons
const featureIcons: Record<string, any> = {
  'Pool': Waves,
  'Garage': Car,
  'Fireplace': Flame,
  'Updated Kitchen': ChefHat,
  'Hardwood Floors': Home,
  'Granite Counters': Home,
  'Stainless Appliances': ChefHat,
  'Master Suite': Home,
  'Patio': TreePine,
  'Basement': Home,
  'Home Office': Home,
  'Walk-in Closet': Home,
  'Security System': Shield,
  'Smart Home': Wifi,
  'Energy Efficient': Lightbulb,
  'Central Air': Wind,
  'Heating': Thermometer,
  'Surveillance': Camera
}

// Categorize features
const categorizeFeatures = (features: string[]) => {
  const categories = {
    'Interior Features': [] as string[],
    'Exterior Features': [] as string[],
    'Appliances & Systems': [] as string[],
    'Other Features': [] as string[]
  }

  const interiorKeywords = ['kitchen', 'floor', 'counter', 'closet', 'office', 'suite', 'basement']
  const exteriorKeywords = ['pool', 'patio', 'garage', 'yard', 'deck', 'balcony']
  const systemKeywords = ['appliance', 'heating', 'air', 'security', 'smart', 'energy']

  features.forEach(feature => {
    const lowerFeature = feature.toLowerCase()
    
    if (interiorKeywords.some(keyword => lowerFeature.includes(keyword))) {
      categories['Interior Features'].push(feature)
    } else if (exteriorKeywords.some(keyword => lowerFeature.includes(keyword))) {
      categories['Exterior Features'].push(feature)
    } else if (systemKeywords.some(keyword => lowerFeature.includes(keyword))) {
      categories['Appliances & Systems'].push(feature)
    } else {
      categories['Other Features'].push(feature)
    }
  })

  return categories
}

export function PropertyFeatures({ features }: PropertyFeaturesProps) {
  const categorizedFeatures = categorizeFeatures(features)

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-6">Property Features</h3>
      
      <div className="space-y-6">
        {Object.entries(categorizedFeatures).map(([category, categoryFeatures]) => {
          if (categoryFeatures.length === 0) return null
          
          return (
            <div key={category}>
              <h4 className="font-medium text-gray-900 mb-3">{category}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {categoryFeatures.map((feature, index) => {
                  const IconComponent = featureIcons[feature] || Home
                  
                  return (
                    <div
                      key={index}
                      className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg"
                    >
                      <IconComponent className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>

      {/* All Features as Badges */}
      <div className="mt-6 pt-6 border-t">
        <h4 className="font-medium text-gray-900 mb-3">All Features</h4>
        <div className="flex flex-wrap gap-2">
          {features.map((feature, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {feature}
            </Badge>
          ))}
        </div>
      </div>
    </Card>
  )
}
