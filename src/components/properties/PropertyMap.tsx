'use client'

import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MapPin, ExternalLink, Navigation } from 'lucide-react'

interface PropertyMapProps {
  address: string
  latitude?: number
  longitude?: number
}

export function PropertyMap({ address, latitude, longitude }: PropertyMapProps) {
  // In a real app, you would integrate with Google Maps, Mapbox, or similar
  const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <MapPin className="h-5 w-5 mr-2" />
          Location & Map
        </h3>
        <Button variant="outline" size="sm" asChild>
          <a href={mapUrl} target="_blank" rel="noopener noreferrer">
            <ExternalLink className="h-4 w-4 mr-2" />
            Open in Maps
          </a>
        </Button>
      </div>

      {/* Address */}
      <div className="mb-4">
        <p className="text-gray-700">{address}</p>
      </div>

      {/* Placeholder Map */}
      <div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden mb-4">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500 text-sm">Interactive map would be displayed here</p>
            <p className="text-gray-400 text-xs">Integration with Google Maps or Mapbox</p>
          </div>
        </div>

        {/* Overlay with address */}
        <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-red-500" />
            <span className="text-sm font-medium">{address}</span>
          </div>
        </div>
      </div>

      {/* Map Actions */}
      <div className="flex flex-wrap gap-2">
        <Button variant="outline" size="sm" asChild>
          <a href={mapUrl} target="_blank" rel="noopener noreferrer">
            <Navigation className="h-4 w-4 mr-2" />
            Get Directions
          </a>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <a
            href={`https://www.google.com/maps/search/schools+near+${encodeURIComponent(address)}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            Nearby Schools
          </a>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <a
            href={`https://www.google.com/maps/search/restaurants+near+${encodeURIComponent(address)}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            Restaurants
          </a>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <a
            href={`https://www.google.com/maps/search/shopping+near+${encodeURIComponent(address)}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            Shopping
          </a>
        </Button>
      </div>

      {/* Neighborhood Info */}
      <div className="mt-6 pt-6 border-t">
        <h4 className="font-medium text-gray-900 mb-3">Neighborhood Highlights</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h5 className="font-medium text-gray-700 mb-2">Transportation</h5>
            <ul className="space-y-1 text-gray-600">
              <li>• Easy access to major highways</li>
              <li>• Public transportation nearby</li>
              <li>• 25 minutes to DFW Airport</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-gray-700 mb-2">Amenities</h5>
            <ul className="space-y-1 text-gray-600">
              <li>• Top-rated schools</li>
              <li>• Shopping centers</li>
              <li>• Parks and recreation</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Location Insights */}
      <div className="mt-6 pt-6 border-t">
        <h4 className="font-medium text-gray-900 mb-3">Location Insights</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-bold text-blue-600">9/10</div>
            <div className="text-xs text-blue-700">School Rating</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-lg font-bold text-green-600">85</div>
            <div className="text-xs text-green-700">Walk Score</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-lg font-bold text-purple-600">Low</div>
            <div className="text-xs text-purple-700">Crime Rate</div>
          </div>
        </div>
      </div>
    </Card>
  )
}
