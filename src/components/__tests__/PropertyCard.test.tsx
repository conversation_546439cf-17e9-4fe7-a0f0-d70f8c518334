import { render, screen, fireEvent } from '@testing-library/react'
import { PropertyCard } from '../PropertyCard'
import { Property } from '@/types'

const mockProperty: Property = {
  id: '1',
  title: 'Test Property',
  description: 'A beautiful test property',
  price: 500000,
  address: '123 Test Street, Test City, TX 12345',
  bedrooms: 3,
  bathrooms: 2.5,
  sqft: 2000,
  images: ['/test-image.jpg'],
  type: 'house',
  status: 'for-sale',
  features: ['Pool', 'Garage', 'Fireplace'],
  yearBuilt: 2020,
  lotSize: 0.5,
  garage: 2,
  neighborhood: 'Test Neighborhood'
}

describe('PropertyCard', () => {
  it('renders property information correctly', () => {
    render(<PropertyCard property={mockProperty} />)
    
    expect(screen.getByText('Test Property')).toBeInTheDocument()
    expect(screen.getByText('$500,000')).toBeInTheDocument()
    expect(screen.getByText('3 bed')).toBeInTheDocument()
    expect(screen.getByText('2.5 bath')).toBeInTheDocument()
    expect(screen.getByText('2,000 sqft')).toBeInTheDocument()
    expect(screen.getByText('123 Test Street, Test City, TX 12345')).toBeInTheDocument()
  })

  it('displays property features', () => {
    render(<PropertyCard property={mockProperty} />)
    
    expect(screen.getByText('Pool')).toBeInTheDocument()
    expect(screen.getByText('Garage')).toBeInTheDocument()
    expect(screen.getByText('Fireplace')).toBeInTheDocument()
  })

  it('shows property status badge', () => {
    render(<PropertyCard property={mockProperty} />)
    
    expect(screen.getByText('for sale')).toBeInTheDocument()
  })

  it('calls onFavorite when favorite button is clicked', () => {
    const mockOnFavorite = jest.fn()
    render(<PropertyCard property={mockProperty} onFavorite={mockOnFavorite} />)
    
    const favoriteButton = screen.getByRole('button', { name: /favorite/i })
    fireEvent.click(favoriteButton)
    
    expect(mockOnFavorite).toHaveBeenCalledWith('1')
  })

  it('displays year built when available', () => {
    render(<PropertyCard property={mockProperty} />)
    
    expect(screen.getByText('Built 2020')).toBeInTheDocument()
  })

  it('handles missing optional properties gracefully', () => {
    const minimalProperty: Property = {
      id: '2',
      title: 'Minimal Property',
      description: 'Basic property',
      price: 300000,
      address: '456 Basic Street',
      bedrooms: 2,
      bathrooms: 1,
      images: [],
      status: 'for-sale',
      features: []
    }

    render(<PropertyCard property={minimalProperty} />)
    
    expect(screen.getByText('Minimal Property')).toBeInTheDocument()
    expect(screen.getByText('$300,000')).toBeInTheDocument()
  })

  it('shows favorited state when isFavorited is true', () => {
    render(<PropertyCard property={mockProperty} isFavorited={true} />)
    
    const favoriteButton = screen.getByRole('button', { name: /favorite/i })
    expect(favoriteButton).toHaveClass('text-red-500') // Assuming favorited state has this class
  })

  it('renders property image with correct alt text', () => {
    render(<PropertyCard property={mockProperty} />)
    
    const image = screen.getByAltText('Test Property')
    expect(image).toBeInTheDocument()
    expect(image).toHaveAttribute('src', '/test-image.jpg')
  })

  it('formats price correctly for different amounts', () => {
    const expensiveProperty = { ...mockProperty, price: 1250000 }
    render(<PropertyCard property={expensiveProperty} />)
    
    expect(screen.getByText('$1,250,000')).toBeInTheDocument()
  })
})
