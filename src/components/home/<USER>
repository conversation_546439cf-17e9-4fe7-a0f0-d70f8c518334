'use client'

import { Card } from '@/components/ui/card'
import { TrendingUp, TrendingDown, Home, DollarSign, Calendar, Users } from 'lucide-react'

const marketData = [
  {
    title: 'Median Home Price',
    value: '$485,000',
    change: '+5.2%',
    trend: 'up' as const,
    icon: DollarSign,
    description: 'vs. last year'
  },
  {
    title: 'Average Days on Market',
    value: '28 days',
    change: '-12%',
    trend: 'down' as const,
    icon: Calendar,
    description: 'vs. last year'
  },
  {
    title: 'Homes Sold',
    value: '2,847',
    change: '+8.1%',
    trend: 'up' as const,
    icon: Home,
    description: 'this year'
  },
  {
    title: 'Active Listings',
    value: '1,234',
    change: '-15%',
    trend: 'down' as const,
    icon: Users,
    description: 'vs. last month'
  }
]

const cityStats = [
  {
    city: 'Plano',
    medianPrice: '$525,000',
    change: '+4.8%',
    trend: 'up' as const
  },
  {
    city: 'Frisco',
    medianPrice: '$485,000',
    change: '+6.2%',
    trend: 'up' as const
  },
  {
    city: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    medianPrice: '$425,000',
    change: '+5.5%',
    trend: 'up' as const
  },
  {
    city: 'Allen',
    medianPrice: '$465,000',
    change: '+4.1%',
    trend: 'up' as const
  },
  {
    city: 'Prosper',
    medianPrice: '$595,000',
    change: '+7.3%',
    trend: 'up' as const
  },
  {
    city: 'Richardson',
    medianPrice: '$445,000',
    change: '+3.9%',
    trend: 'up' as const
  }
]

export function MarketStats() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Collin County Market Insights
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Stay informed with the latest real estate market trends and statistics
          </p>
        </div>

        {/* Overall Market Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {marketData.map((stat, index) => (
            <Card key={index} className="p-6 text-center hover:shadow-lg transition-shadow">
              <div className="flex justify-center mb-4">
                <div className={`p-3 rounded-full ${
                  stat.trend === 'up' ? 'bg-green-100' : 'bg-red-100'
                }`}>
                  <stat.icon className={`h-6 w-6 ${
                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`} />
                </div>
              </div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">{stat.title}</h3>
              <div className="text-2xl font-bold text-gray-900 mb-2">{stat.value}</div>
              <div className="flex items-center justify-center space-x-1">
                {stat.trend === 'up' ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span className={`text-sm font-medium ${
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500">{stat.description}</span>
              </div>
            </Card>
          ))}
        </div>

        {/* City-by-City Breakdown */}
        <Card className="p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Median Home Prices by City
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {cityStats.map((city, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-semibold text-gray-900">{city.city}</h4>
                  <p className="text-2xl font-bold text-blue-600">{city.medianPrice}</p>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">{city.change}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Market Summary */}
        <div className="mt-12 text-center">
          <Card className="p-8 bg-blue-50 border-blue-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Market Summary</h3>
            <p className="text-gray-700 max-w-3xl mx-auto">
              The Collin County real estate market continues to show strong growth with increasing home values 
              and steady demand. With excellent schools, growing job opportunities, and family-friendly communities, 
              the area remains one of Texas&apos;s most desirable places to live.
            </p>
          </Card>
        </div>
      </div>
    </section>
  )
}
