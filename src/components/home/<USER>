'use client'

import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MapPin, School, ShoppingBag, Coffee, TreePine, Star } from 'lucide-react'

const neighborhoods = [
  {
    name: 'West Plano',
    description: 'Prestigious area known for luxury homes and top-rated schools',
    medianPrice: '$525,000',
    rating: 9.2,
    highlights: ['Top Schools', 'Luxury Homes', 'Shopping Centers'],
    amenities: [
      { icon: School, label: 'Excellent Schools' },
      { icon: ShoppingBag, label: 'Legacy West' },
      { icon: TreePine, label: 'Parks & Trails' }
    ],
    image: '/neighborhood-plano.jpg'
  },
  {
    name: 'Frisco Square',
    description: 'Vibrant downtown area with entertainment, dining, and modern living',
    medianPrice: '$485,000',
    rating: 9.0,
    highlights: ['Entertainment', 'Dining', 'Sports Venues'],
    amenities: [
      { icon: Coffee, label: 'Restaurants' },
      { icon: ShoppingBag, label: 'Shopping' },
      { icon: Star, label: 'Events' }
    ],
    image: '/neighborhood-frisco.jpg'
  },
  {
    name: 'Historic McKinney',
    description: 'Charming historic downtown with character homes and local businesses',
    medianPrice: '$425,000',
    rating: 8.5,
    highlights: ['Historic Charm', 'Local Businesses', 'Community Events'],
    amenities: [
      { icon: Coffee, label: 'Local Cafes' },
      { icon: TreePine, label: 'Historic Square' },
      { icon: Star, label: 'Festivals' }
    ],
    image: '/neighborhood-mckinney.jpg'
  },
  {
    name: 'Allen Station',
    description: 'Family-friendly community with excellent schools and recreation',
    medianPrice: '$465,000',
    rating: 8.8,
    highlights: ['Family-Friendly', 'Recreation', 'New Development'],
    amenities: [
      { icon: School, label: 'Top Schools' },
      { icon: TreePine, label: 'Recreation Center' },
      { icon: ShoppingBag, label: 'Watters Creek' }
    ],
    image: '/neighborhood-allen.jpg'
  },
  {
    name: 'Prosper Commons',
    description: 'Rapidly growing area with new construction and modern amenities',
    medianPrice: '$595,000',
    rating: 9.1,
    highlights: ['New Construction', 'Growth', 'Modern Amenities'],
    amenities: [
      { icon: School, label: 'New Schools' },
      { icon: ShoppingBag, label: 'Shopping Centers' },
      { icon: TreePine, label: 'Parks' }
    ],
    image: '/neighborhood-prosper.jpg'
  },
  {
    name: 'Richardson Heights',
    description: 'Established community with mature trees and convenient location',
    medianPrice: '$445,000',
    rating: 8.3,
    highlights: ['Established', 'Convenient', 'Mature Trees'],
    amenities: [
      { icon: Coffee, label: 'Local Dining' },
      { icon: TreePine, label: 'Mature Trees' },
      { icon: MapPin, label: 'Central Location' }
    ],
    image: '/neighborhood-richardson.jpg'
  }
]

export function NeighborhoodGuide() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Explore Collin County Neighborhoods
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover the unique character and amenities of each community to find your perfect neighborhood
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {neighborhoods.map((neighborhood, index) => (
            <Card key={index} className="overflow-hidden hover:shadow-xl transition-shadow">
              {/* Neighborhood Image */}
              <div className="h-48 bg-gradient-to-br from-blue-400 to-blue-600 relative">
                <div className="absolute inset-0 bg-black/20" />
                <div className="absolute top-4 right-4">
                  <Badge className="bg-white text-gray-900">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    {neighborhood.rating}
                  </Badge>
                </div>
                <div className="absolute bottom-4 left-4">
                  <h3 className="text-xl font-bold text-white">{neighborhood.name}</h3>
                  <p className="text-blue-100">Median: {neighborhood.medianPrice}</p>
                </div>
              </div>

              <div className="p-6">
                <p className="text-gray-600 mb-4">{neighborhood.description}</p>

                {/* Highlights */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {neighborhood.highlights.map((highlight, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {highlight}
                    </Badge>
                  ))}
                </div>

                {/* Amenities */}
                <div className="space-y-2 mb-6">
                  {neighborhood.amenities.map((amenity, idx) => (
                    <div key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                      <amenity.icon className="h-4 w-4 text-blue-600" />
                      <span>{amenity.label}</span>
                    </div>
                  ))}
                </div>

                <Button variant="outline" className="w-full">
                  <MapPin className="h-4 w-4 mr-2" />
                  Explore {neighborhood.name}
                </Button>
              </div>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button size="lg" className="px-8">
            View Complete Neighborhood Guide
          </Button>
        </div>
      </div>
    </section>
  )
}
