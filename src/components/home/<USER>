'use client'

import { PropertyCard } from '@/components/PropertyCard'
import { Button } from '@/components/ui/button'
import { ArrowRight } from 'lucide-react'

// Mock data for featured properties
const featuredProperties = [
  {
    id: '1',
    title: 'Luxury Estate in Plano',
    price: 850000,
    address: '123 Oak Tree Lane, Plano, TX 75024',
    bedrooms: 5,
    bathrooms: 4.5,
    sqft: 4200,
    images: ['/property-1.jpg'],
    type: 'house' as const,
    status: 'for-sale' as const,
    features: ['Pool', 'Garage', 'Fireplace', 'Updated Kitchen'],
    description: 'Stunning luxury home in prestigious Plano neighborhood with top-rated schools.',
    yearBuilt: 2018,
    lotSize: 0.75,
    garage: 3,
    neighborhood: 'West Plano',
    schools: {
      elementary: 'Plano Elementary (9/10)',
      middle: 'Plano Middle School (8/10)',
      high: 'Plano Senior High (9/10)'
    }
  },
  {
    id: '2',
    title: 'Modern Townhome in Frisco',
    price: 425000,
    address: '456 Maple Street, Frisco, TX 75034',
    bedrooms: 3,
    bathrooms: 2.5,
    sqft: 2100,
    images: ['/property-2.jpg'],
    type: 'townhouse' as const,
    status: 'for-sale' as const,
    features: ['Patio', 'Garage', 'Open Floor Plan'],
    description: 'Beautiful modern townhome in the heart of Frisco with easy access to shopping and dining.',
    yearBuilt: 2020,
    lotSize: 0.15,
    garage: 2,
    neighborhood: 'Frisco Square',
    schools: {
      elementary: 'Frisco Elementary (9/10)',
      middle: 'Frisco Middle School (9/10)',
      high: 'Frisco High School (8/10)'
    }
  },
  {
    id: '3',
    title: 'Family Home in McKinney',
    price: 375000,
    address: '789 Pine Avenue, McKinney, TX 75070',
    bedrooms: 4,
    bathrooms: 3,
    sqft: 2800,
    images: ['/property-3.jpg'],
    type: 'house' as const,
    status: 'for-sale' as const,
    features: ['Large Yard', 'Garage', 'Updated Bathrooms'],
    description: 'Perfect family home in established McKinney neighborhood with mature trees.',
    yearBuilt: 2015,
    lotSize: 0.5,
    garage: 2,
    neighborhood: 'Historic McKinney',
    schools: {
      elementary: 'McKinney Elementary (8/10)',
      middle: 'McKinney Middle School (8/10)',
      high: 'McKinney High School (7/10)'
    }
  },
  {
    id: '4',
    title: 'New Construction in Allen',
    price: 525000,
    address: '321 Cedar Drive, Allen, TX 75013',
    bedrooms: 4,
    bathrooms: 3.5,
    sqft: 3200,
    images: ['/property-4.jpg'],
    type: 'house' as const,
    status: 'for-sale' as const,
    features: ['New Construction', 'Smart Home', 'Energy Efficient'],
    description: 'Brand new construction with all the latest features and smart home technology.',
    yearBuilt: 2024,
    lotSize: 0.4,
    garage: 2,
    neighborhood: 'Allen Station',
    schools: {
      elementary: 'Allen Elementary (9/10)',
      middle: 'Allen Middle School (9/10)',
      high: 'Allen High School (9/10)'
    }
  },
  {
    id: '5',
    title: 'Luxury Condo in Prosper',
    price: 295000,
    address: '654 Willow Creek, Prosper, TX 75078',
    bedrooms: 2,
    bathrooms: 2,
    sqft: 1400,
    images: ['/property-5.jpg'],
    type: 'condo' as const,
    status: 'for-sale' as const,
    features: ['Balcony', 'Pool Access', 'Fitness Center'],
    description: 'Luxury condo with resort-style amenities in growing Prosper community.',
    yearBuilt: 2021,
    lotSize: 0,
    garage: 1,
    neighborhood: 'Prosper Commons',
    schools: {
      elementary: 'Prosper Elementary (9/10)',
      middle: 'Prosper Middle School (9/10)',
      high: 'Prosper High School (9/10)'
    }
  },
  {
    id: '6',
    title: 'Executive Home in Richardson',
    price: 675000,
    address: '987 Elm Street, Richardson, TX 75081',
    bedrooms: 5,
    bathrooms: 4,
    sqft: 3800,
    images: ['/property-6.jpg'],
    type: 'house' as const,
    status: 'for-sale' as const,
    features: ['Pool', 'Home Office', 'Wine Cellar'],
    description: 'Executive home with premium finishes and resort-style backyard.',
    yearBuilt: 2017,
    lotSize: 0.6,
    garage: 3,
    neighborhood: 'Richardson Heights',
    schools: {
      elementary: 'Richardson Elementary (8/10)',
      middle: 'Richardson Middle School (8/10)',
      high: 'Richardson High School (8/10)'
    }
  }
]

export function FeaturedProperties() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured Properties
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover our handpicked selection of premium homes in Collin County&apos;s most desirable neighborhoods
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {featuredProperties.map((property) => (
            <PropertyCard key={property.id} property={property} />
          ))}
        </div>

        <div className="text-center">
          <Button size="lg" variant="outline" className="px-8">
            View All Properties
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  )
}
