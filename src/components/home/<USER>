'use client'

import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Award, 
  Clock, 
  Shield, 
  TrendingUp, 
  Heart,
  Phone,
  CheckCircle
} from 'lucide-react'

const features = [
  {
    icon: Users,
    title: 'Local Expertise',
    description: 'Born and raised in Collin County, we know every neighborhood, school district, and community inside and out.',
    stats: '25+ Years Local Experience'
  },
  {
    icon: Award,
    title: 'Proven Track Record',
    description: 'Top-rated real estate team with hundreds of successful transactions and satisfied clients.',
    stats: '2,500+ Homes Sold'
  },
  {
    icon: Clock,
    title: 'Fast Response Time',
    description: 'We respond to inquiries within minutes, not hours. Your time is valuable, and we respect that.',
    stats: 'Average 5-Minute Response'
  },
  {
    icon: Shield,
    title: 'Full-Service Support',
    description: 'From initial search to closing day, we handle every detail to ensure a smooth transaction.',
    stats: 'End-to-End Service'
  },
  {
    icon: TrendingUp,
    title: 'Market Insights',
    description: 'Access to exclusive market data and trends to help you make informed decisions.',
    stats: 'Real-Time Market Data'
  },
  {
    icon: Heart,
    title: 'Client-Focused',
    description: 'Your satisfaction is our priority. We go above and beyond to exceed your expectations.',
    stats: '98% Client Satisfaction'
  }
]

const testimonialHighlights = [
  {
    quote: "Made our home buying process seamless and stress-free.",
    author: "Sarah & <PERSON>",
    location: "Plano"
  },
  {
    quote: "Sold our home in just 5 days above asking price!",
    author: "David Chen",
    location: "Frisco"
  },
  {
    quote: "Incredible knowledge of the local market and schools.",
    author: "Jennifer Martinez",
    location: "McKinney"
  }
]

export function WhyChooseUs() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose CollinCounty.homes?
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Experience the difference of working with Collin County&apos;s most trusted real estate professionals
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card key={index} className="p-6 text-center hover:shadow-lg transition-shadow">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600 mb-4">{feature.description}</p>
              <div className="text-sm font-medium text-blue-600">{feature.stats}</div>
            </Card>
          ))}
        </div>

        {/* Testimonial Highlights */}
        <div className="bg-white rounded-2xl p-8 mb-12">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            What Our Clients Say
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonialHighlights.map((testimonial, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
                <blockquote className="text-gray-700 italic mb-3">
                  &quot;{testimonial.quote}&quot;
                </blockquote>
                <div className="text-sm">
                  <div className="font-medium text-gray-900">{testimonial.author}</div>
                  <div className="text-gray-500">{testimonial.location}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <Card className="p-8 bg-blue-600 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Whether you&apos;re buying your first home or selling your current one, 
            we&apos;re here to guide you through every step of the process.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="px-8">
              <Phone className="h-5 w-5 mr-2" />
              Call (*************
            </Button>
            <Button size="lg" variant="outline" className="px-8 border-white text-white hover:bg-white hover:text-blue-600">
              Schedule Consultation
            </Button>
          </div>
        </Card>
      </div>
    </section>
  )
}
