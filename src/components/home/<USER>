'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react'

const testimonials = [
  {
    id: 1,
    name: '<PERSON> & <PERSON>',
    location: 'Plano, TX',
    rating: 5,
    text: 'Working with the CollinCounty.homes team was an absolute pleasure. They helped us find our dream home in West Plano and made the entire process seamless. Their knowledge of the local market and school districts was invaluable.',
    property: 'Purchased 4BR/3BA in West Plano',
    date: 'November 2023'
  },
  {
    id: 2,
    name: '<PERSON>',
    location: 'Frisco, TX',
    rating: 5,
    text: 'I needed to sell my home quickly due to a job relocation. The team not only sold my house in just 5 days but got me $15,000 above asking price! Their marketing strategy and negotiation skills are top-notch.',
    property: 'Sold 3BR/2BA in Frisco Square',
    date: 'October 2023'
  },
  {
    id: 3,
    name: '<PERSON>',
    location: 'McKinney, TX',
    rating: 5,
    text: 'As a first-time homebuyer, I was nervous about the process. The team walked me through every step, answered all my questions, and helped me find the perfect starter home in a great neighborhood.',
    property: 'Purchased 3BR/2BA in Historic McKinney',
    date: 'December 2023'
  },
  {
    id: 4,
    name: '<PERSON> & Lisa <PERSON>',
    location: 'Allen, <PERSON>',
    rating: 5,
    text: 'We had been looking for a home for months with another agent with no success. Within two weeks of working with CollinCounty.homes, we found and closed on our perfect family home. Highly recommended!',
    property: 'Purchased 5BR/4BA in Allen Station',
    date: 'September 2023'
  },
  {
    id: 5,
    name: 'Amanda <PERSON>',
    location: 'Prosper, TX',
    rating: 5,
    text: 'The team helped us navigate the competitive Prosper market and secure our dream home. Their responsiveness and attention to detail made all the difference in a fast-moving market.',
    property: 'Purchased 4BR/3.5BA in Prosper Commons',
    date: 'January 2024'
  },
  {
    id: 6,
    name: 'Mark Williams',
    location: 'Richardson, TX',
    rating: 5,
    text: 'Professional, knowledgeable, and genuinely caring. They went above and beyond to help us sell our home and find a new one that better fit our growing family. Couldn\'t be happier with the service.',
    property: 'Sold & Purchased in Richardson Heights',
    date: 'August 2023'
  }
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const testimonialsPerPage = 3

  const nextTestimonials = () => {
    setCurrentIndex((prev) => 
      prev + testimonialsPerPage >= testimonials.length ? 0 : prev + testimonialsPerPage
    )
  }

  const prevTestimonials = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? Math.max(0, testimonials.length - testimonialsPerPage) : prev - testimonialsPerPage
    )
  }

  const currentTestimonials = testimonials.slice(currentIndex, currentIndex + testimonialsPerPage)

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Client Success Stories
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Don&apos;t just take our word for it. Here&apos;s what our satisfied clients have to say about their experience
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
          {currentTestimonials.map((testimonial) => (
            <Card key={testimonial.id} className="p-6 hover:shadow-lg transition-shadow">
              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Quote */}
              <div className="relative mb-6">
                <Quote className="absolute -top-2 -left-2 h-8 w-8 text-blue-200" />
                <p className="text-gray-700 italic pl-6">{testimonial.text}</p>
              </div>

              {/* Client Info */}
              <div className="border-t pt-4">
                <div className="font-semibold text-gray-900">{testimonial.name}</div>
                <div className="text-sm text-gray-600">{testimonial.location}</div>
                <div className="text-sm text-blue-600 mt-1">{testimonial.property}</div>
                <div className="text-xs text-gray-500 mt-1">{testimonial.date}</div>
              </div>
            </Card>
          ))}
        </div>

        {/* Navigation */}
        <div className="flex justify-center items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={prevTestimonials}
            disabled={currentIndex === 0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex space-x-2">
            {Array.from({ length: Math.ceil(testimonials.length / testimonialsPerPage) }).map((_, i) => (
              <button
                key={i}
                onClick={() => setCurrentIndex(i * testimonialsPerPage)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  Math.floor(currentIndex / testimonialsPerPage) === i
                    ? 'bg-blue-600'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={nextTestimonials}
            disabled={currentIndex + testimonialsPerPage >= testimonials.length}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="p-8 bg-gray-50">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Join Our Success Stories?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Let us help you achieve your real estate goals. Contact us today for a free consultation.
            </p>
            <Button size="lg" className="px-8">
              Get Started Today
            </Button>
          </Card>
        </div>
      </div>
    </section>
  )
}
