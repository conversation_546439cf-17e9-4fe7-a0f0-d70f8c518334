import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database schema types
export interface DatabaseProperty {
  id: string
  title: string
  description: string
  price: number
  address: string
  city: string
  state: string
  zip_code: string
  bedrooms: number
  bathrooms: number
  square_footage: number
  lot_size?: number
  year_built?: number
  garage_spaces?: number
  property_type: 'house' | 'townhouse' | 'condo' | 'land'
  status: 'for-sale' | 'sold' | 'pending' | 'off-market'
  images: string[]
  features: string[]
  neighborhood?: string
  school_district?: string
  elementary_school?: string
  middle_school?: string
  high_school?: string
  hoa_fees?: number
  tax_amount?: number
  days_on_market?: number
  mls_number?: string
  agent_id?: string
  latitude?: number
  longitude?: number
  created_at: string
  updated_at: string
}

export interface DatabaseAgent {
  id: string
  name: string
  email: string
  phone: string
  bio?: string
  image?: string
  license_number?: string
  years_experience?: number
  specialties: string[]
  social_media?: {
    facebook?: string
    instagram?: string
    linkedin?: string
    twitter?: string
  }
  created_at: string
  updated_at: string
}

export interface DatabaseUser {
  id: string
  email: string
  name: string
  role: 'buyer' | 'agent' | 'admin'
  phone?: string
  preferences?: {
    saved_searches: any[]
    favorite_properties: string[]
    notifications: boolean
  }
  created_at: string
  updated_at: string
}

export interface DatabaseContact {
  id: string
  name: string
  email: string
  phone?: string
  message: string
  property_id?: string
  preferred_contact: 'email' | 'phone'
  status: 'new' | 'contacted' | 'closed'
  created_at: string
  updated_at: string
}

// Property queries
export const propertyQueries = {
  async getAll(filters?: {
    minPrice?: number
    maxPrice?: number
    bedrooms?: number
    bathrooms?: number
    propertyType?: string
    city?: string
    status?: string
    limit?: number
    offset?: number
  }) {
    let query = supabase
      .from('properties')
      .select('*')
      .eq('status', filters?.status || 'for-sale')

    if (filters?.minPrice) {
      query = query.gte('price', filters.minPrice)
    }
    if (filters?.maxPrice) {
      query = query.lte('price', filters.maxPrice)
    }
    if (filters?.bedrooms) {
      query = query.gte('bedrooms', filters.bedrooms)
    }
    if (filters?.bathrooms) {
      query = query.gte('bathrooms', filters.bathrooms)
    }
    if (filters?.propertyType && filters.propertyType !== 'any') {
      query = query.eq('property_type', filters.propertyType)
    }
    if (filters?.city) {
      query = query.ilike('city', `%${filters.city}%`)
    }

    if (filters?.limit) {
      query = query.limit(filters.limit)
    }
    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
    }

    return query.order('created_at', { ascending: false })
  },

  async getById(id: string) {
    return supabase
      .from('properties')
      .select('*')
      .eq('id', id)
      .single()
  },

  async create(property: Omit<DatabaseProperty, 'id' | 'created_at' | 'updated_at'>) {
    return supabase
      .from('properties')
      .insert([{
        ...property,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single()
  },

  async update(id: string, updates: Partial<DatabaseProperty>) {
    return supabase
      .from('properties')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
  },

  async delete(id: string) {
    return supabase
      .from('properties')
      .delete()
      .eq('id', id)
  },

  async search(query: string) {
    return supabase
      .from('properties')
      .select('*')
      .or(`title.ilike.%${query}%,address.ilike.%${query}%,city.ilike.%${query}%,neighborhood.ilike.%${query}%`)
      .eq('status', 'for-sale')
      .order('created_at', { ascending: false })
  }
}

// Agent queries
export const agentQueries = {
  async getAll() {
    return supabase
      .from('agents')
      .select('*')
      .order('name')
  },

  async getById(id: string) {
    return supabase
      .from('agents')
      .select('*')
      .eq('id', id)
      .single()
  },

  async create(agent: Omit<DatabaseAgent, 'id' | 'created_at' | 'updated_at'>) {
    return supabase
      .from('agents')
      .insert([{
        ...agent,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single()
  },

  async update(id: string, updates: Partial<DatabaseAgent>) {
    return supabase
      .from('agents')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
  }
}

// Contact queries
export const contactQueries = {
  async create(contact: Omit<DatabaseContact, 'id' | 'created_at' | 'updated_at' | 'status'>) {
    return supabase
      .from('contacts')
      .insert([{
        ...contact,
        status: 'new',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single()
  },

  async getAll(status?: string) {
    let query = supabase
      .from('contacts')
      .select('*, properties(title)')

    if (status) {
      query = query.eq('status', status)
    }

    return query.order('created_at', { ascending: false })
  },

  async updateStatus(id: string, status: 'new' | 'contacted' | 'closed') {
    return supabase
      .from('contacts')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
  }
}

// User queries
export const userQueries = {
  async getById(id: string) {
    return supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single()
  },

  async updatePreferences(id: string, preferences: any) {
    return supabase
      .from('users')
      .update({
        preferences,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
  }
}
