export const PROPERTY_TYPES = [
  { value: 'single-family', label: 'Single Family' },
  { value: 'townhouse', label: 'Townhouse' },
  { value: 'condo', label: 'Condo' },
  { value: 'land', label: 'Land' },
  { value: 'commercial', label: 'Commercial' },
] as const

export const PROPERTY_STATUS = [
  { value: 'for-sale', label: 'For Sale' },
  { value: 'sold', label: 'Sold' },
  { value: 'pending', label: 'Pending' },
  { value: 'off-market', label: 'Off Market' },
] as const

export const COLLIN_COUNTY_CITIES = [
  'Allen',
  'Anna',
  'Blue Ridge',
  'Ce<PERSON>',
  'Fairview',
  'Farmersville',
  'Frisco',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Nevada',
  'Parker',
  'Plano',
  'Princeton',
  'Prosper',
  'Richardson',
  'Rockwall',
  'Royse City',
  'Sachse',
  'St. Paul',
  '<PERSON> Al<PERSON>ne',
  'Wylie',
] as const

export const POPULAR_FEATURES = [
  'Swimming Pool',
  'Fireplace',
  'Hardwood Floors',
  'Granite Countertops',
  'Stainless Steel Appliances',
  'Walk-in Closet',
  'Two-Car Garage',
  'Covered Patio',
  'Updated Kitchen',
  'Master Suite',
  'Fenced Yard',
  'Energy Efficient',
  'Open Floor Plan',
  'Vaulted Ceilings',
  'Tile Flooring',
  'Security System',
  'Sprinkler System',
  'Storage Space',
] as const

export const BEDROOM_OPTIONS = [
  { value: 1, label: '1+' },
  { value: 2, label: '2+' },
  { value: 3, label: '3+' },
  { value: 4, label: '4+' },
  { value: 5, label: '5+' },
] as const

export const BATHROOM_OPTIONS = [
  { value: 1, label: '1+' },
  { value: 1.5, label: '1.5+' },
  { value: 2, label: '2+' },
  { value: 2.5, label: '2.5+' },
  { value: 3, label: '3+' },
  { value: 3.5, label: '3.5+' },
  { value: 4, label: '4+' },
] as const

export const PRICE_RANGES = [
  { min: 0, max: 200000, label: 'Under $200K' },
  { min: 200000, max: 300000, label: '$200K - $300K' },
  { min: 300000, max: 400000, label: '$300K - $400K' },
  { min: 400000, max: 500000, label: '$400K - $500K' },
  { min: 500000, max: 750000, label: '$500K - $750K' },
  { min: 750000, max: 1000000, label: '$750K - $1M' },
  { min: 1000000, max: Infinity, label: '$1M+' },
] as const

export const SORT_OPTIONS = [
  { value: 'newest', label: 'Newest First' },
  { value: 'oldest', label: 'Oldest First' },
  { value: 'price-asc', label: 'Price: Low to High' },
  { value: 'price-desc', label: 'Price: High to Low' },
  { value: 'size-asc', label: 'Size: Small to Large' },
  { value: 'size-desc', label: 'Size: Large to Small' },
] as const
