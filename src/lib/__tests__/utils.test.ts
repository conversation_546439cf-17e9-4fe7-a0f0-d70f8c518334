import { formatPrice, formatNumber, cn } from '../utils'

describe('formatPrice', () => {
  it('formats prices correctly', () => {
    expect(formatPrice(500000)).toBe('$500,000')
    expect(formatPrice(1250000)).toBe('$1,250,000')
    expect(formatPrice(75000)).toBe('$75,000')
    expect(formatPrice(0)).toBe('$0')
  })

  it('handles negative prices', () => {
    expect(formatPrice(-100000)).toBe('-$100,000')
  })

  it('handles decimal prices', () => {
    expect(formatPrice(500000.50)).toBe('$500,000.50')
    expect(formatPrice(500000.00)).toBe('$500,000')
  })
})

describe('formatNumber', () => {
  it('formats numbers with commas', () => {
    expect(formatNumber(1000)).toBe('1,000')
    expect(formatNumber(1000000)).toBe('1,000,000')
    expect(formatNumber(2500)).toBe('2,500')
    expect(formatNumber(100)).toBe('100')
  })

  it('handles zero and negative numbers', () => {
    expect(formatNumber(0)).toBe('0')
    expect(formatNumber(-1000)).toBe('-1,000')
  })

  it('handles decimal numbers', () => {
    expect(formatNumber(1000.5)).toBe('1,000.5')
    expect(formatNumber(1000.00)).toBe('1,000')
  })
})

describe('cn (className utility)', () => {
  it('combines class names correctly', () => {
    expect(cn('class1', 'class2')).toBe('class1 class2')
  })

  it('handles conditional classes', () => {
    expect(cn('base', true && 'conditional', false && 'hidden')).toBe('base conditional')
  })

  it('handles undefined and null values', () => {
    expect(cn('base', undefined, null, 'end')).toBe('base end')
  })

  it('handles empty strings', () => {
    expect(cn('base', '', 'end')).toBe('base end')
  })

  it('deduplicates classes', () => {
    expect(cn('class1 class2', 'class2 class3')).toBe('class1 class2 class3')
  })

  it('handles complex conditional logic', () => {
    const isActive = true
    const isDisabled = false
    const variant = 'primary'
    
    const result = cn(
      'base-class',
      isActive && 'active',
      isDisabled && 'disabled',
      variant === 'primary' && 'primary-variant'
    )
    
    expect(result).toBe('base-class active primary-variant')
  })
})
