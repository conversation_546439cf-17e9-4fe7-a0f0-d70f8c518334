/**
 * @license lucide-react v0.516.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m10 15 5 5 5-5", key: "1hpjnr" }],
  ["path", { d: "M4 4h7a4 4 0 0 1 4 4v12", key: "wcbgct" }]
];
const CornerRightDown = createLucideIcon("corner-right-down", __iconNode);

export { __iconNode, CornerRightDown as default };
//# sourceMappingURL=corner-right-down.js.map
