/**
 * @license lucide-react v0.516.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10.5 2v4", key: "1xt6in" }],
  ["path", { d: "M14 2H7a2 2 0 0 0-2 2", key: "e6xig3" }],
  [
    "path",
    {
      d: "M19.29 14.76A6.67 6.67 0 0 1 17 11a6.6 6.6 0 0 1-2.29 3.76c-1.15.92-1.71 2.04-1.71 3.19 0 2.22 1.8 4.05 4 4.05s4-1.83 4-4.05c0-1.16-.57-2.26-1.71-3.19",
      key: "adq7uc"
    }
  ],
  [
    "path",
    {
      d: "M9.607 21H6a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h7V7a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3",
      key: "t9hm96"
    }
  ]
];
const SoapDispenserDroplet = createLucideIcon("soap-dispenser-droplet", __iconNode);

export { __iconNode, SoapDispenserDroplet as default };
//# sourceMappingURL=soap-dispenser-droplet.js.map
