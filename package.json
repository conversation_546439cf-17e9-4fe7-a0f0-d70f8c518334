{"name": "collin-county-homes", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test && npm run test:e2e", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.0", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jest": "^29.5.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "@playwright/test": "^1.40.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}