-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create properties table
CREATE TABLE properties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  price INTEGER NOT NULL,
  address VARCHAR(500) NOT NULL,
  city VARCHAR(100) NOT NULL,
  state VARCHAR(2) NOT NULL DEFAULT 'TX',
  zip_code VARCHAR(10) NOT NULL,
  bedrooms INTEGER NOT NULL,
  bathrooms DECIMAL(3,1) NOT NULL,
  square_footage INTEGER,
  lot_size DECIMAL(10,2),
  year_built INTEGER,
  garage_spaces INTEGER DEFAULT 0,
  property_type VARCHAR(20) NOT NULL CHECK (property_type IN ('house', 'townhouse', 'condo', 'land')),
  status VARCHAR(20) NOT NULL DEFAULT 'for-sale' CHECK (status IN ('for-sale', 'sold', 'pending', 'off-market')),
  images JSONB DEFAULT '[]',
  features <PERSON><PERSON><PERSON><PERSON> DEFAULT '[]',
  neighborhood VARCHAR(100),
  school_district VARCHAR(100),
  elementary_school VARCHAR(100),
  middle_school VARCHAR(100),
  high_school VARCHAR(100),
  hoa_fees INTEGER,
  tax_amount INTEGER,
  days_on_market INTEGER,
  mls_number VARCHAR(50),
  agent_id UUID,
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create agents table
CREATE TABLE agents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  bio TEXT,
  image VARCHAR(500),
  license_number VARCHAR(50),
  years_experience INTEGER,
  specialties JSONB DEFAULT '[]',
  social_media JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'buyer' CHECK (role IN ('buyer', 'agent', 'admin')),
  phone VARCHAR(20),
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contacts table
CREATE TABLE contacts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  message TEXT NOT NULL,
  property_id UUID REFERENCES properties(id),
  preferred_contact VARCHAR(10) DEFAULT 'email' CHECK (preferred_contact IN ('email', 'phone')),
  status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'closed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create saved_searches table
CREATE TABLE saved_searches (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  filters JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create favorite_properties table
CREATE TABLE favorite_properties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, property_id)
);

-- Create indexes for better performance
CREATE INDEX idx_properties_status ON properties(status);
CREATE INDEX idx_properties_price ON properties(price);
CREATE INDEX idx_properties_bedrooms ON properties(bedrooms);
CREATE INDEX idx_properties_bathrooms ON properties(bathrooms);
CREATE INDEX idx_properties_property_type ON properties(property_type);
CREATE INDEX idx_properties_city ON properties(city);
CREATE INDEX idx_properties_neighborhood ON properties(neighborhood);
CREATE INDEX idx_properties_created_at ON properties(created_at);
CREATE INDEX idx_properties_agent_id ON properties(agent_id);

CREATE INDEX idx_contacts_status ON contacts(status);
CREATE INDEX idx_contacts_created_at ON contacts(created_at);
CREATE INDEX idx_contacts_property_id ON contacts(property_id);

CREATE INDEX idx_agents_email ON agents(email);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- Add foreign key constraint for agent_id
ALTER TABLE properties ADD CONSTRAINT fk_properties_agent_id 
  FOREIGN KEY (agent_id) REFERENCES agents(id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_properties_updated_at BEFORE UPDATE ON properties
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_saved_searches_updated_at BEFORE UPDATE ON saved_searches
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_searches ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorite_properties ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Properties: Public read access for active listings, authenticated users can read all
CREATE POLICY "Public can view active properties" ON properties
  FOR SELECT USING (status = 'for-sale');

CREATE POLICY "Authenticated users can view all properties" ON properties
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Agents can manage their properties" ON properties
  FOR ALL USING (agent_id = auth.uid() OR auth.jwt() ->> 'role' = 'admin');

-- Agents: Public read access
CREATE POLICY "Public can view agents" ON agents
  FOR SELECT USING (true);

CREATE POLICY "Agents can update their profile" ON agents
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can manage agents" ON agents
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Users: Users can only access their own data
CREATE POLICY "Users can view their own data" ON users
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can update their own data" ON users
  FOR UPDATE USING (id = auth.uid());

-- Contacts: Agents and admins can view all, others can only create
CREATE POLICY "Anyone can create contacts" ON contacts
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Agents and admins can view contacts" ON contacts
  FOR SELECT USING (auth.jwt() ->> 'role' IN ('agent', 'admin'));

CREATE POLICY "Agents and admins can update contacts" ON contacts
  FOR UPDATE USING (auth.jwt() ->> 'role' IN ('agent', 'admin'));

-- Saved searches: Users can only access their own
CREATE POLICY "Users can manage their saved searches" ON saved_searches
  FOR ALL USING (user_id = auth.uid());

-- Favorite properties: Users can only access their own
CREATE POLICY "Users can manage their favorite properties" ON favorite_properties
  FOR ALL USING (user_id = auth.uid());
