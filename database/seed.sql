-- Insert sample agents
INSERT INTO agents (id, name, email, phone, bio, license_number, years_experience, specialties) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  '<PERSON>',
  '<EMAIL>',
  '(*************',
  '<PERSON> has been helping families find their dream homes in Collin County for over 10 years. She specializes in luxury properties and new construction.',
  '***********',
  10,
  '["Luxury Homes", "New Construction", "First-Time Buyers"]'
),
(
  '550e8400-e29b-41d4-a716-446655440002',
  '<PERSON>',
  '<EMAIL>',
  '(*************',
  '<PERSON> is a top-performing agent with expertise in investment properties and commercial real estate. He has closed over 500 transactions.',
  '***********',
  8,
  '["Investment Properties", "Commercial Real Estate", "Relocation"]'
),
(
  '550e8400-e29b-41d4-a716-446655440003',
  '<PERSON>',
  'jennifer.martine<PERSON>@collincounty.homes',
  '(*************',
  '<PERSON> focuses on helping young families find homes in top school districts. She is known for her attention to detail and excellent communication.',
  '***********',
  6,
  '["Family Homes", "School Districts", "First-Time Buyers"]'
);

-- Insert sample properties
INSERT INTO properties (
  id, title, description, price, address, city, state, zip_code, bedrooms, bathrooms, 
  square_footage, lot_size, year_built, garage_spaces, property_type, status, 
  images, features, neighborhood, school_district, elementary_school, middle_school, high_school,
  mls_number, agent_id, latitude, longitude
) VALUES
(
  '660e8400-e29b-41d4-a716-446655440001',
  'Luxury Estate in West Plano',
  'Stunning luxury home in prestigious West Plano neighborhood with top-rated schools. This beautiful estate features an open floor plan, gourmet kitchen with granite countertops, spacious master suite, and resort-style backyard with pool.',
  850000,
  '123 Oak Tree Lane',
  'Plano',
  'TX',
  '75024',
  5,
  4.5,
  4200,
  0.75,
  2018,
  3,
  'house',
  'for-sale',
  '["/property-1.jpg", "/property-1-2.jpg", "/property-1-3.jpg"]',
  '["Pool", "Garage", "Fireplace", "Updated Kitchen", "Hardwood Floors", "Granite Counters"]',
  'West Plano',
  'Plano ISD',
  'Plano Elementary (9/10)',
  'Plano Middle School (8/10)',
  'Plano Senior High (9/10)',
  'MLS123456',
  '550e8400-e29b-41d4-a716-446655440001',
  33.0198,
  -96.6989
),
(
  '660e8400-e29b-41d4-a716-446655440002',
  'Modern Townhome in Frisco Square',
  'Beautiful modern townhome in the heart of Frisco with easy access to shopping and dining. Features include open floor plan, updated kitchen, and private patio.',
  425000,
  '456 Maple Street',
  'Frisco',
  'TX',
  '75034',
  3,
  2.5,
  2100,
  0.15,
  2020,
  2,
  'townhouse',
  'for-sale',
  '["/property-2.jpg", "/property-2-2.jpg"]',
  '["Patio", "Garage", "Open Floor Plan", "Updated Kitchen"]',
  'Frisco Square',
  'Frisco ISD',
  'Frisco Elementary (9/10)',
  'Frisco Middle School (9/10)',
  'Frisco High School (8/10)',
  'MLS234567',
  '550e8400-e29b-41d4-a716-446655440002',
  33.1507,
  -96.8236
),
(
  '660e8400-e29b-41d4-a716-446655440003',
  'Family Home in Historic McKinney',
  'Perfect family home in established McKinney neighborhood with mature trees. Features spacious bedrooms, updated bathrooms, and large backyard.',
  375000,
  '789 Pine Avenue',
  'McKinney',
  'TX',
  '75070',
  4,
  3.0,
  2800,
  0.5,
  2015,
  2,
  'house',
  'for-sale',
  '["/property-3.jpg"]',
  '["Large Yard", "Garage", "Updated Bathrooms", "Mature Trees"]',
  'Historic McKinney',
  'McKinney ISD',
  'McKinney Elementary (8/10)',
  'McKinney Middle School (8/10)',
  'McKinney High School (7/10)',
  'MLS345678',
  '550e8400-e29b-41d4-a716-446655440003',
  33.1972,
  -96.6397
),
(
  '660e8400-e29b-41d4-a716-446655440004',
  'New Construction in Allen Station',
  'Brand new construction with all the latest features and smart home technology. Energy efficient with modern finishes throughout.',
  525000,
  '321 Cedar Drive',
  'Allen',
  'TX',
  '75013',
  4,
  3.5,
  3200,
  0.4,
  2024,
  2,
  'house',
  'for-sale',
  '["/property-4.jpg"]',
  '["New Construction", "Smart Home", "Energy Efficient", "Modern Finishes"]',
  'Allen Station',
  'Allen ISD',
  'Allen Elementary (9/10)',
  'Allen Middle School (9/10)',
  'Allen High School (9/10)',
  'MLS456789',
  '550e8400-e29b-41d4-a716-446655440001',
  33.1031,
  -96.6706
),
(
  '660e8400-e29b-41d4-a716-446655440005',
  'Luxury Condo in Prosper Commons',
  'Luxury condo with resort-style amenities in growing Prosper community. Features include balcony, pool access, and fitness center.',
  295000,
  '654 Willow Creek',
  'Prosper',
  'TX',
  '75078',
  2,
  2.0,
  1400,
  0,
  2021,
  1,
  'condo',
  'for-sale',
  '["/property-5.jpg"]',
  '["Balcony", "Pool Access", "Fitness Center", "Modern Appliances"]',
  'Prosper Commons',
  'Prosper ISD',
  'Prosper Elementary (9/10)',
  'Prosper Middle School (9/10)',
  'Prosper High School (9/10)',
  'MLS567890',
  '550e8400-e29b-41d4-a716-446655440002',
  33.2362,
  -96.8011
),
(
  '660e8400-e29b-41d4-a716-446655440006',
  'Executive Home in Richardson Heights',
  'Executive home with premium finishes and resort-style backyard. Features include pool, home office, and wine cellar.',
  675000,
  '987 Elm Street',
  'Richardson',
  'TX',
  '75081',
  5,
  4.0,
  3800,
  0.6,
  2017,
  3,
  'house',
  'for-sale',
  '["/property-6.jpg"]',
  '["Pool", "Home Office", "Wine Cellar", "Premium Finishes"]',
  'Richardson Heights',
  'Richardson ISD',
  'Richardson Elementary (8/10)',
  'Richardson Middle School (8/10)',
  'Richardson High School (8/10)',
  'MLS678901',
  '550e8400-e29b-41d4-a716-446655440003',
  32.9483,
  -96.7299
),
(
  '660e8400-e29b-41d4-a716-446655440007',
  'Starter Home in Carrollton',
  'Perfect starter home for first-time buyers. Recently updated with new flooring and paint throughout.',
  285000,
  '159 Birch Lane',
  'Carrollton',
  'TX',
  '75006',
  3,
  2.0,
  1650,
  0.25,
  2010,
  2,
  'house',
  'for-sale',
  '["/property-7.jpg"]',
  '["Updated Flooring", "Fresh Paint", "Garage", "Fenced Yard"]',
  'Carrollton Meadows',
  'Carrollton-Farmers Branch ISD',
  'Carrollton Elementary (7/10)',
  'Carrollton Middle School (7/10)',
  'Carrollton High School (7/10)',
  'MLS789012',
  '550e8400-e29b-41d4-a716-446655440001',
  32.9537,
  -96.8903
),
(
  '660e8400-e29b-41d4-a716-446655440008',
  'Luxury Townhome in The Colony',
  'Upscale townhome in gated community with lake access. Features include granite counters, stainless appliances, and community amenities.',
  395000,
  '753 Lake View Drive',
  'The Colony',
  'TX',
  '75056',
  3,
  2.5,
  2200,
  0.1,
  2019,
  2,
  'townhouse',
  'for-sale',
  '["/property-8.jpg"]',
  '["Lake Access", "Gated Community", "Granite Counters", "Stainless Appliances"]',
  'Lake Vista',
  'Lewisville ISD',
  'The Colony Elementary (8/10)',
  'The Colony Middle School (8/10)',
  'The Colony High School (8/10)',
  'MLS890123',
  '550e8400-e29b-41d4-a716-446655440002',
  33.0807,
  -96.8928
);

-- Insert sample users
INSERT INTO users (id, email, name, role, phone) VALUES
(
  '770e8400-e29b-41d4-a716-446655440001',
  '<EMAIL>',
  'Admin User',
  'admin',
  '(*************'
),
(
  '770e8400-e29b-41d4-a716-446655440002',
  '<EMAIL>',
  'John Smith',
  'buyer',
  '(*************'
),
(
  '770e8400-e29b-41d4-a716-446655440003',
  '<EMAIL>',
  'Emily Davis',
  'buyer',
  '(*************'
);

-- Insert sample contacts
INSERT INTO contacts (name, email, phone, message, property_id, preferred_contact) VALUES
(
  'Robert Wilson',
  '<EMAIL>',
  '(*************',
  'I am interested in scheduling a showing for this property. Please contact me at your earliest convenience.',
  '660e8400-e29b-41d4-a716-446655440001',
  'phone'
),
(
  'Lisa Thompson',
  '<EMAIL>',
  '(*************',
  'This property looks perfect for our family. Can you provide more information about the school district and neighborhood?',
  '660e8400-e29b-41d4-a716-446655440003',
  'email'
),
(
  'David Brown',
  '<EMAIL>',
  '(*************',
  'I would like to know more about the HOA fees and community amenities for this townhome.',
  '660e8400-e29b-41d4-a716-446655440002',
  'email'
);
