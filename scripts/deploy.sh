#!/bin/bash

# Deployment Script for Collin County Homes
# This script handles deployment to different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="staging"
SKIP_TESTS=false
SKIP_BUILD=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -e, --environment ENV    Deployment environment (staging|production)"
            echo "  --skip-tests            Skip running tests"
            echo "  --skip-build            Skip building the application"
            echo "  -h, --help              Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
    exit 1
fi

print_status "🚀 Starting deployment to $ENVIRONMENT environment..."

# Check if we're on the correct branch
CURRENT_BRANCH=$(git branch --show-current)
if [[ "$ENVIRONMENT" == "production" && "$CURRENT_BRANCH" != "main" ]]; then
    print_error "Production deployments must be from the 'main' branch. Current branch: $CURRENT_BRANCH"
    exit 1
elif [[ "$ENVIRONMENT" == "staging" && "$CURRENT_BRANCH" != "develop" && "$CURRENT_BRANCH" != "main" ]]; then
    print_warning "Staging deployments are typically from 'develop' branch. Current branch: $CURRENT_BRANCH"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check for uncommitted changes
if [[ -n $(git status --porcelain) ]]; then
    print_error "You have uncommitted changes. Please commit or stash them before deploying."
    git status --short
    exit 1
fi

# Pull latest changes
print_status "Pulling latest changes..."
git pull origin $CURRENT_BRANCH

# Install dependencies
print_status "Installing dependencies..."
npm ci

# Run tests unless skipped
if [[ "$SKIP_TESTS" == false ]]; then
    print_status "Running tests..."
    
    # Run linting
    print_status "Running ESLint..."
    npm run lint
    
    # Run type checking
    print_status "Running TypeScript type check..."
    npm run type-check
    
    # Run unit tests
    print_status "Running unit tests..."
    npm run test
    
    print_success "All tests passed"
else
    print_warning "Skipping tests as requested"
fi

# Build application unless skipped
if [[ "$SKIP_BUILD" == false ]]; then
    print_status "Building application..."
    npm run build
    print_success "Build completed"
else
    print_warning "Skipping build as requested"
fi

# Environment-specific deployment
case $ENVIRONMENT in
    staging)
        print_status "Deploying to staging environment..."
        
        # Deploy to Vercel staging
        if command -v vercel &> /dev/null; then
            vercel --token $VERCEL_TOKEN
        else
            print_error "Vercel CLI not found. Please install it: npm i -g vercel"
            exit 1
        fi
        
        DEPLOYMENT_URL="https://collin-county-homes-staging.vercel.app"
        ;;
        
    production)
        print_status "Deploying to production environment..."
        
        # Confirm production deployment
        print_warning "You are about to deploy to PRODUCTION!"
        read -p "Are you sure you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Deployment cancelled"
            exit 0
        fi
        
        # Deploy to Vercel production
        if command -v vercel &> /dev/null; then
            vercel --prod --token $VERCEL_TOKEN
        else
            print_error "Vercel CLI not found. Please install it: npm i -g vercel"
            exit 1
        fi
        
        DEPLOYMENT_URL="https://collincounty.homes"
        ;;
esac

# Wait for deployment to be ready
print_status "Waiting for deployment to be ready..."
sleep 30

# Health check
print_status "Performing health check..."
if curl -f "$DEPLOYMENT_URL/api/health" > /dev/null 2>&1; then
    print_success "Health check passed"
else
    print_error "Health check failed. Deployment may have issues."
    exit 1
fi

# Run post-deployment tests
if [[ "$ENVIRONMENT" == "production" ]]; then
    print_status "Running post-deployment smoke tests..."
    
    # Basic connectivity tests
    if curl -f "$DEPLOYMENT_URL" > /dev/null 2>&1; then
        print_success "Homepage is accessible"
    else
        print_error "Homepage is not accessible"
        exit 1
    fi
    
    if curl -f "$DEPLOYMENT_URL/properties" > /dev/null 2>&1; then
        print_success "Properties page is accessible"
    else
        print_error "Properties page is not accessible"
        exit 1
    fi
    
    # Check if sitemap is accessible
    if curl -f "$DEPLOYMENT_URL/sitemap.xml" > /dev/null 2>&1; then
        print_success "Sitemap is accessible"
    else
        print_warning "Sitemap is not accessible"
    fi
    
    # Check if robots.txt is accessible
    if curl -f "$DEPLOYMENT_URL/robots.txt" > /dev/null 2>&1; then
        print_success "Robots.txt is accessible"
    else
        print_warning "Robots.txt is not accessible"
    fi
fi

# Create deployment record
DEPLOYMENT_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
cat > "deployments/deployment-$ENVIRONMENT-$(date +%Y%m%d-%H%M%S).json" << EOF
{
  "environment": "$ENVIRONMENT",
  "timestamp": "$DEPLOYMENT_TIME",
  "branch": "$CURRENT_BRANCH",
  "commit": "$(git rev-parse HEAD)",
  "url": "$DEPLOYMENT_URL",
  "deployer": "$(git config user.name)",
  "version": "$(npm pkg get version | tr -d '"')"
}
EOF

# Success message
print_success "🎉 Deployment to $ENVIRONMENT completed successfully!"
print_status "Deployment URL: $DEPLOYMENT_URL"
print_status "Deployment time: $DEPLOYMENT_TIME"
print_status "Git commit: $(git rev-parse --short HEAD)"

# Send notification (if configured)
if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚀 Collin County Homes deployed to $ENVIRONMENT successfully!\nURL: $DEPLOYMENT_URL\nCommit: $(git rev-parse --short HEAD)\"}" \
        $SLACK_WEBHOOK_URL
fi

print_status "Deployment completed! 🚀"
