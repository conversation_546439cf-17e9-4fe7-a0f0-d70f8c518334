#!/bin/bash

# Test Setup Script for Collin County Homes
# This script sets up the testing environment and runs all tests

set -e  # Exit on any error

echo "🚀 Setting up test environment for Collin County Homes..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_success "Node.js version check passed: $(node -v)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed."
    exit 1
fi

print_success "npm version: $(npm -v)"

# Install dependencies
print_status "Installing dependencies..."
npm ci

if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Create test environment file if it doesn't exist
if [ ! -f .env.test ]; then
    print_status "Creating test environment file..."
    cat > .env.test << EOF
# Test Environment Variables
NODE_ENV=test
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=https://test.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=test_key
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-TEST123
NEXT_PUBLIC_GTM_ID=GTM-TEST123
NEXT_PUBLIC_FB_PIXEL_ID=123456789
EOF
    print_success "Test environment file created"
fi

# Run linting
print_status "Running ESLint..."
npm run lint

if [ $? -eq 0 ]; then
    print_success "Linting passed"
else
    print_warning "Linting issues found. Please fix them before proceeding."
fi

# Run type checking
print_status "Running TypeScript type check..."
npm run type-check

if [ $? -eq 0 ]; then
    print_success "Type checking passed"
else
    print_error "Type checking failed"
    exit 1
fi

# Run unit tests
print_status "Running unit tests..."
npm run test:coverage

if [ $? -eq 0 ]; then
    print_success "Unit tests passed"
else
    print_error "Unit tests failed"
    exit 1
fi

# Build the application
print_status "Building application..."
npm run build

if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

# Install Playwright browsers if not already installed
print_status "Installing Playwright browsers..."
npx playwright install --with-deps

if [ $? -eq 0 ]; then
    print_success "Playwright browsers installed"
else
    print_warning "Failed to install Playwright browsers. E2E tests may fail."
fi

# Start the application in background for E2E tests
print_status "Starting application for E2E tests..."
npm run start &
APP_PID=$!

# Wait for the application to start
print_status "Waiting for application to start..."
sleep 10

# Check if the application is running
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    print_success "Application is running"
else
    print_error "Application failed to start"
    kill $APP_PID 2>/dev/null
    exit 1
fi

# Run E2E tests
print_status "Running E2E tests..."
npm run test:e2e

E2E_EXIT_CODE=$?

# Stop the application
print_status "Stopping application..."
kill $APP_PID 2>/dev/null

if [ $E2E_EXIT_CODE -eq 0 ]; then
    print_success "E2E tests passed"
else
    print_error "E2E tests failed"
    exit 1
fi

# Run security audit
print_status "Running security audit..."
npm audit --audit-level=high

if [ $? -eq 0 ]; then
    print_success "Security audit passed"
else
    print_warning "Security vulnerabilities found. Please review and fix them."
fi

# Generate test report
print_status "Generating test report..."
cat > test-report.md << EOF
# Test Report - $(date)

## Summary
- ✅ Dependencies installed
- ✅ Linting passed
- ✅ Type checking passed
- ✅ Unit tests passed
- ✅ Build successful
- ✅ E2E tests passed
- ✅ Security audit completed

## Coverage
See \`coverage/lcov-report/index.html\` for detailed coverage report.

## E2E Test Results
See \`playwright-report/index.html\` for detailed E2E test results.

## Next Steps
1. Review any warnings or issues mentioned above
2. Deploy to staging environment
3. Run additional manual testing if needed
4. Deploy to production

EOF

print_success "Test report generated: test-report.md"

print_success "🎉 All tests completed successfully!"
print_status "You can now proceed with deployment."

# Open coverage report if running locally
if [ "$CI" != "true" ]; then
    if command -v open &> /dev/null; then
        print_status "Opening coverage report..."
        open coverage/lcov-report/index.html
    elif command -v xdg-open &> /dev/null; then
        print_status "Opening coverage report..."
        xdg-open coverage/lcov-report/index.html
    fi
fi
