{"version": 2, "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "framework": "nextjs", "regions": ["iad1"], "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=300, s-maxage=300"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/property/:id", "destination": "/properties/:id", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "env": {"NEXT_PUBLIC_SITE_URL": "@next_public_site_url", "NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "NEXT_PUBLIC_GA_MEASUREMENT_ID": "@next_public_ga_measurement_id", "NEXT_PUBLIC_GTM_ID": "@next_public_gtm_id", "NEXT_PUBLIC_FB_PIXEL_ID": "@next_public_fb_pixel_id"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}}