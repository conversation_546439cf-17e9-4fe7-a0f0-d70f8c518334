# Deployment Guide - Collin County Homes

This guide covers the deployment process for the Collin County Homes real estate website.

## Prerequisites

- Node.js 18 or higher
- npm or yarn package manager
- Git
- Vercel CLI (for Vercel deployments)
- Docker (for containerized deployments)

## Environment Setup

### 1. Environment Variables

Create the following environment files:

#### `.env.local` (Development)
```bash
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_ga_id
NEXT_PUBLIC_GTM_ID=your_gtm_id
NEXT_PUBLIC_FB_PIXEL_ID=your_fb_pixel_id
```

#### `.env.staging` (Staging)
```bash
NEXT_PUBLIC_SITE_URL=https://staging.collincounty.homes
NEXT_PUBLIC_SUPABASE_URL=your_staging_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_supabase_anon_key
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_staging_ga_id
NEXT_PUBLIC_GTM_ID=your_staging_gtm_id
NEXT_PUBLIC_FB_PIXEL_ID=your_staging_fb_pixel_id
```

#### `.env.production` (Production)
```bash
NEXT_PUBLIC_SITE_URL=https://collincounty.homes
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_production_ga_id
NEXT_PUBLIC_GTM_ID=your_production_gtm_id
NEXT_PUBLIC_FB_PIXEL_ID=your_production_fb_pixel_id
```

### 2. Database Setup

1. Set up Supabase project
2. Run the database schema: `database/schema.sql`
3. Seed initial data: `database/seed.sql`
4. Configure Row Level Security policies

## Testing

### Automated Testing

Run the complete test suite:
```bash
# Make script executable
chmod +x scripts/test-setup.sh

# Run all tests
./scripts/test-setup.sh
```

### Manual Testing

1. **Unit Tests**: `npm run test`
2. **E2E Tests**: `npm run test:e2e`
3. **Type Checking**: `npm run type-check`
4. **Linting**: `npm run lint`
5. **Build Test**: `npm run build`

## Deployment Options

### Option 1: Vercel (Recommended)

#### Quick Deploy
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/collin-county-homes)

#### Manual Deployment

1. Install Vercel CLI:
```bash
npm i -g vercel
```

2. Login to Vercel:
```bash
vercel login
```

3. Deploy to staging:
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh --environment staging
```

4. Deploy to production:
```bash
./scripts/deploy.sh --environment production
```

### Option 2: Docker Deployment

1. Build Docker image:
```bash
docker build -t collin-county-homes .
```

2. Run with Docker Compose:
```bash
docker-compose up -d
```

3. Access the application at `http://localhost:3000`

### Option 3: Traditional Hosting

1. Build the application:
```bash
npm run build
```

2. Start the production server:
```bash
npm start
```

## CI/CD Pipeline

The project includes a GitHub Actions workflow (`.github/workflows/ci-cd.yml`) that:

1. Runs tests on every push and PR
2. Builds the application
3. Runs security audits
4. Deploys to staging (develop branch)
5. Deploys to production (main branch)
6. Runs Lighthouse performance tests
7. Sends notifications

### Setting up GitHub Actions

1. Add the following secrets to your GitHub repository:
   - `VERCEL_TOKEN`
   - `VERCEL_ORG_ID`
   - `VERCEL_PROJECT_ID`
   - `NEXT_PUBLIC_SITE_URL`
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - `SLACK_WEBHOOK_URL` (optional)
   - `SNYK_TOKEN` (optional)

2. Push to the `develop` branch to trigger staging deployment
3. Push to the `main` branch to trigger production deployment

## Performance Monitoring

### Lighthouse CI

The project includes Lighthouse CI configuration for performance monitoring:

```bash
npm install -g @lhci/cli
lhci autorun
```

### Analytics

The application includes:
- Google Analytics 4
- Google Tag Manager
- Facebook Pixel
- Custom performance monitoring

## Security

### Security Headers

The application includes security headers:
- X-Frame-Options
- X-Content-Type-Options
- Referrer-Policy
- Permissions-Policy

### Content Security Policy

Configure CSP in `next.config.js` for additional security.

### Security Auditing

Run security audits:
```bash
npm audit
npm audit --audit-level=high
```

## Monitoring and Maintenance

### Health Checks

The application includes health check endpoints:
- `/api/health` - Basic health check
- `/api/status` - Detailed status information

### Logging

Configure logging for production:
- Application logs
- Error tracking (Sentry recommended)
- Performance monitoring

### Backup Strategy

1. **Database**: Regular Supabase backups
2. **Media Files**: Cloud storage backups
3. **Code**: Git repository backups

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version (18+)
   - Clear npm cache: `npm cache clean --force`
   - Delete node_modules and reinstall

2. **Environment Variables**
   - Verify all required variables are set
   - Check variable names (case-sensitive)
   - Restart application after changes

3. **Database Connection**
   - Verify Supabase URL and keys
   - Check network connectivity
   - Review RLS policies

4. **Performance Issues**
   - Run Lighthouse audit
   - Check bundle size: `npm run analyze`
   - Review Core Web Vitals

### Support

For deployment issues:
1. Check the deployment logs
2. Review the troubleshooting section
3. Contact the development team

## Rollback Procedure

### Vercel Rollback
```bash
vercel rollback [deployment-url]
```

### Docker Rollback
```bash
docker-compose down
docker-compose up -d --scale app=0
# Deploy previous version
docker-compose up -d
```

## Post-Deployment Checklist

- [ ] Health checks pass
- [ ] All pages load correctly
- [ ] Forms submit successfully
- [ ] Search functionality works
- [ ] Analytics tracking active
- [ ] SEO meta tags present
- [ ] Performance metrics acceptable
- [ ] Security headers configured
- [ ] SSL certificate valid
- [ ] Database connections working
- [ ] Error monitoring active

## Maintenance Schedule

- **Daily**: Monitor error logs and performance
- **Weekly**: Review analytics and user feedback
- **Monthly**: Security updates and dependency updates
- **Quarterly**: Performance optimization and feature updates

---

For more information, see the main [README.md](README.md) file.
